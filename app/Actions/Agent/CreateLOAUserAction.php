<?php

namespace App\Actions\Agent;

use App\Actions\Action;
use App\Models\Agent;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Mail\LOAWelcomeNotification;

class CreateLOAUserAction extends Action
{
    /**
     * Create a new LOA user under an agent's management.
     *
     * @param array $data
     * @return array
     */
    public function execute(array $data): array
    {
        try {
            DB::beginTransaction();

            // Validate the managing agent
            $managingAgent = Agent::find($data['managing_agent_id']);
            if (!$managingAgent || $managingAgent->status !== 'approved') {
                return [
                    'success' => false,
                    'message' => 'Invalid or inactive managing agent'
                ];
            }

            // Check if agent can create LOAs
            if (!$this->canAgentCreateLOAs($managingAgent)) {
                return [
                    'success' => false,
                    'message' => 'Agent is not authorized to create LOA users'
                ];
            }

            // Create the LOA user
            $loaUser = $this->createLOAUser($data, $managingAgent);

            // Send welcome notification
            $this->sendWelcomeNotification($loaUser, $managingAgent, $data);

            DB::commit();

            Log::info('LOA user created successfully', [
                'loa_user_id' => $loaUser->id,
                'managing_agent_id' => $managingAgent->id,
                'created_by' => $data['created_by'] ?? null
            ]);

            return [
                'success' => true,
                'message' => 'LOA user created successfully',
                'loa_user' => $loaUser,
                'managing_agent' => $managingAgent
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to create LOA user', [
                'error' => $e->getMessage(),
                'data' => $data,
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to create LOA user: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Check if agent can create LOA users.
     *
     * @param Agent $agent
     * @return bool
     */
    private function canAgentCreateLOAs(Agent $agent): bool
    {
        // Only certain tiers can create LOAs
        $allowedTiers = ['AGENT', 'MGA', 'SVG', 'FMO', 'SFMO'];
        
        if (!in_array($agent->tier, $allowedTiers)) {
            return false;
        }

        // Agent must be approved and active
        if ($agent->status !== 'approved' || $agent->user->status !== 'active') {
            return false;
        }

        // Check if agent has reached LOA limit (optional business rule)
        $currentLOACount = $agent->managedLOAs()->count();
        $maxLOAs = $this->getMaxLOAsForTier($agent->tier);
        
        return $currentLOACount < $maxLOAs;
    }

    /**
     * Get maximum LOAs allowed for each tier.
     *
     * @param string $tier
     * @return int
     */
    private function getMaxLOAsForTier(string $tier): int
    {
        $limits = [
            'AGENT' => 5,
            'MGA' => 15,
            'SVG' => 25,
            'FMO' => 50,
            'SFMO' => 100
        ];

        return $limits[$tier] ?? 0;
    }

    /**
     * Create the LOA user.
     *
     * @param array $data
     * @param Agent $managingAgent
     * @return User
     */
    private function createLOAUser(array $data, Agent $managingAgent): User
    {
        // Generate temporary password if not provided
        $password = $data['password'] ?? $this->generateTemporaryPassword();

        $loaUser = User::create([
            'fname' => $data['first_name'],
            'lname' => $data['last_name'],
            'email' => $data['email'],
            'password' => Hash::make($password),
            'role' => 'loa',
            'status' => 'active',
            'phone' => $data['phone'] ?? null,
            'address1' => $data['address1'] ?? null,
            'city' => $data['city'] ?? null,
            'state' => $data['state'] ?? null,
            'zip' => $data['zip'] ?? null,
            'managing_agent_id' => $managingAgent->id
        ]);

        // Store temporary password for welcome email
        $loaUser->temporary_password = $password;

        return $loaUser;
    }

    /**
     * Send welcome notification to new LOA user.
     *
     * @param User $loaUser
     * @param Agent $managingAgent
     * @param array $data
     * @return void
     */
    private function sendWelcomeNotification(User $loaUser, Agent $managingAgent, array $data): void
    {
        try {
            if (class_exists(LOAWelcomeNotification::class)) {
                Mail::to($loaUser->email)->send(new LOAWelcomeNotification($loaUser, $managingAgent));
            }

            // Notify managing agent
            if (isset($data['notify_agent']) && $data['notify_agent']) {
                $managingAgent->user->notify(new \App\Notifications\LOAUserCreatedNotification($loaUser));
            }

        } catch (\Exception $e) {
            Log::warning('Failed to send LOA welcome notifications', [
                'loa_user_id' => $loaUser->id,
                'managing_agent_id' => $managingAgent->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Generate a temporary password for new LOA user.
     *
     * @return string
     */
    private function generateTemporaryPassword(): string
    {
        return 'LOA-' . strtoupper(substr(md5(uniqid(rand(), true)), 0, 8));
    }

    /**
     * Get LOA creation statistics for an agent.
     *
     * @param Agent $agent
     * @return array
     */
    public function getLOAStatistics(Agent $agent): array
    {
        $managedLOAs = $agent->managedLOAs();
        $maxAllowed = $this->getMaxLOAsForTier($agent->tier);

        return [
            'current_count' => $managedLOAs->count(),
            'max_allowed' => $maxAllowed,
            'remaining' => max(0, $maxAllowed - $managedLOAs->count()),
            'active_count' => $managedLOAs->where('status', 'active')->count(),
            'recent_referrals' => $this->getRecentLOAReferrals($agent),
            'total_loa_referrals' => $this->getTotalLOAReferrals($agent)
        ];
    }

    /**
     * Get recent referrals from agent's LOAs.
     *
     * @param Agent $agent
     * @return int
     */
    private function getRecentLOAReferrals(Agent $agent): int
    {
        // This would query the loa_referrals table
        return DB::table('loa_referrals')
            ->whereIn('loa_user_id', $agent->managedLOAs()->pluck('id'))
            ->where('created_at', '>=', now()->subDays(30))
            ->count();
    }

    /**
     * Get total referrals from agent's LOAs.
     *
     * @param Agent $agent
     * @return int
     */
    private function getTotalLOAReferrals(Agent $agent): int
    {
        return DB::table('loa_referrals')
            ->whereIn('loa_user_id', $agent->managedLOAs()->pluck('id'))
            ->count();
    }
}
