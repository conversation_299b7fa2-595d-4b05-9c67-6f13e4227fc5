<?php

namespace App\Actions\Agents;

use App\Actions\Action;
use App\Models\Agent;
use App\Models\AgentCommission;
use App\Models\Subscription;
use App\Models\Transaction;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CalculateCommissionAction extends Action
{
    /**
     * Calculate and record commissions for a transaction.
     *
     * @param array $data
     * @return array
     */
    public function execute(array $data): array
    {
        try {
            DB::beginTransaction();

            $transaction = Transaction::find($data['transaction_id']);
            $subscription = Subscription::find($data['subscription_id'] ?? null);

            if (!$transaction) {
                return [
                    'success' => false,
                    'message' => 'Transaction not found'
                ];
            }

            // Get the agent associated with this transaction (if any)
            $user = $transaction->user;
            $agent = null;

            // Check if this transaction was referred by an agent
            if ($user && isset($data['agent_id'])) {
                $agent = Agent::find($data['agent_id']);
            }

            // If no direct agent, check if user was referred by an LOA
            if (!$agent && $user && $user->referring_agent_id) {
                $referringUser = User::find($user->referring_agent_id);

                // If referred by LOA, get the LOA's managing agent for commission
                if ($referringUser && $referringUser->isLOA() && $referringUser->managingAgent) {
                    $agent = $referringUser->managingAgent;

                    Log::info('Commission calculated for LOA-referred patient', [
                        'patient_id' => $user->id,
                        'loa_user_id' => $referringUser->id,
                        'managing_agent_id' => $agent->id,
                        'transaction_id' => $transaction->id
                    ]);
                }
            }

            // If no agent is associated, no commission to calculate
            if (!$agent) {
                return [
                    'success' => true,
                    'message' => 'No agent associated with this transaction',
                    'commission' => null
                ];
            }

            // Check if agent is eligible for commission (excludes LOA)
            if (!$agent->user || !$agent->user->isEligibleForCommission()) {
                return [
                    'success' => true,
                    'message' => 'Agent is not eligible for commission (LOA or inactive)',
                    'commission' => null
                ];
            }

            // Calculate agent's commission
            $totalAmount = $transaction->amount;
            $commissionAmount = $agent->calculateCommission($totalAmount);

            // Create commission record
            $commission = new AgentCommission([
                'agent_id' => $agent->id,
                'transaction_id' => $transaction->id,
                'subscription_id' => $subscription ? $subscription->id : null,
                'total_amount' => $totalAmount,
                'commission_amount' => $commissionAmount,
                'agent_rate' => $agent->commission_rate,
                'status' => 'pending'
            ]);

            // Check for upline commission
            if ($agent->referring_agent_id) {
                $uplineAgent = $agent->referrer;

                if ($uplineAgent) {
                    $uplineCommissionAmount = $uplineAgent->calculateUplineCommission($totalAmount, $agent);

                    if ($uplineCommissionAmount > 0) {
                        $commission->upline_agent_id = $uplineAgent->id;
                        $commission->upline_commission_amount = $uplineCommissionAmount;
                        $commission->upline_rate = $uplineAgent->commission_rate - $agent->commission_rate;
                    }
                }
            }

            $commission->save();

            DB::commit();

            return [
                'success' => true,
                'message' => 'Commission calculated successfully',
                'commission' => $commission
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Commission calculation failed: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => 'Commission calculation failed: ' . $e->getMessage()
            ];
        }
    }
}
