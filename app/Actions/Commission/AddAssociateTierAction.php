<?php

namespace App\Actions\Commission;

use App\Actions\Action;
use App\Models\Agent;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AddAssociateTierAction extends Action
{
    /**
     * Add ASSOCIATE tier functionality to the commission system.
     *
     * @param array $data
     * @return array
     */
    public function execute(array $data): array
    {
        try {
            DB::beginTransaction();

            // Validate that ASSOCIATE tier is properly configured
            if (!isset(Agent::TIERS['ASSOCIATE'])) {
                return [
                    'success' => false,
                    'message' => 'ASSOCIATE tier not found in Agent::TIERS configuration'
                ];
            }

            // Validate that ASSOCIATE tier hierarchy is properly configured
            if (!isset(Agent::TIER_HIERARCHY['ASSOCIATE'])) {
                return [
                    'success' => false,
                    'message' => 'ASSOCIATE tier not found in Agent::TIER_HIERARCHY configuration'
                ];
            }

            // Update any existing agents that should be ASSOCIATE tier
            if (isset($data['update_existing_agents']) && $data['update_existing_agents']) {
                $this->updateExistingAgentsToAssociate($data);
            }

            // Create default ASSOCIATE tier agent if requested
            if (isset($data['create_sample_associate']) && $data['create_sample_associate']) {
                $this->createSampleAssociateAgent($data);
            }

            DB::commit();

            Log::info('ASSOCIATE tier successfully added to commission system', [
                'tier_rate' => Agent::TIERS['ASSOCIATE'],
                'tier_hierarchy' => Agent::TIER_HIERARCHY['ASSOCIATE']
            ]);

            return [
                'success' => true,
                'message' => 'ASSOCIATE tier successfully added to commission system',
                'tier_info' => [
                    'name' => 'ASSOCIATE',
                    'rate' => Agent::TIERS['ASSOCIATE'],
                    'hierarchy' => Agent::TIER_HIERARCHY['ASSOCIATE']
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to add ASSOCIATE tier to commission system', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to add ASSOCIATE tier: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Update existing agents to ASSOCIATE tier based on criteria.
     *
     * @param array $data
     * @return void
     */
    private function updateExistingAgentsToAssociate(array $data): void
    {
        $criteria = $data['associate_criteria'] ?? [];
        
        // Default criteria: agents with low commission volume or new agents
        $query = Agent::query();

        // Apply criteria filters
        if (isset($criteria['max_commission_earned'])) {
            $query->whereHas('commissions', function ($q) use ($criteria) {
                $q->havingRaw('SUM(commission_amount) <= ?', [$criteria['max_commission_earned']]);
            });
        }

        if (isset($criteria['created_after'])) {
            $query->where('created_at', '>=', $criteria['created_after']);
        }

        if (isset($criteria['current_tier'])) {
            $query->where('tier', $criteria['current_tier']);
        }

        // Update matching agents to ASSOCIATE tier
        $updatedCount = $query->update([
            'tier' => 'ASSOCIATE',
            'commission_rate' => Agent::TIERS['ASSOCIATE']
        ]);

        Log::info('Updated existing agents to ASSOCIATE tier', [
            'updated_count' => $updatedCount,
            'criteria' => $criteria
        ]);
    }

    /**
     * Create a sample ASSOCIATE tier agent for testing.
     *
     * @param array $data
     * @return void
     */
    private function createSampleAssociateAgent(array $data): void
    {
        if (!isset($data['sample_user_data'])) {
            return;
        }

        $userData = $data['sample_user_data'];

        // Create user
        $user = User::create([
            'first_name' => $userData['first_name'] ?? 'Sample',
            'last_name' => $userData['last_name'] ?? 'Associate',
            'email' => $userData['email'] ?? '<EMAIL>',
            'password' => bcrypt($userData['password'] ?? 'password'),
            'role' => 'agent',
            'status' => 'active'
        ]);

        // Create agent profile
        $agent = Agent::create([
            'user_id' => $user->id,
            'company' => $userData['company'] ?? 'Sample Company',
            'experience' => $userData['experience'] ?? 'entry_level',
            'status' => 'approved',
            'tier' => 'ASSOCIATE',
            'commission_rate' => Agent::TIERS['ASSOCIATE'],
            'npn' => $userData['npn'] ?? '12345678'
        ]);

        // Generate referral code
        $agent->generateReferralCode();

        Log::info('Created sample ASSOCIATE tier agent', [
            'user_id' => $user->id,
            'agent_id' => $agent->id,
            'tier' => 'ASSOCIATE'
        ]);
    }

    /**
     * Validate ASSOCIATE tier configuration.
     *
     * @return array
     */
    public function validateAssociateTierConfiguration(): array
    {
        $issues = [];

        // Check if ASSOCIATE tier exists in TIERS
        if (!isset(Agent::TIERS['ASSOCIATE'])) {
            $issues[] = 'ASSOCIATE tier missing from Agent::TIERS';
        }

        // Check if ASSOCIATE tier exists in TIER_HIERARCHY
        if (!isset(Agent::TIER_HIERARCHY['ASSOCIATE'])) {
            $issues[] = 'ASSOCIATE tier missing from Agent::TIER_HIERARCHY';
        }

        // Check if ASSOCIATE tier has correct hierarchy position (should be 0)
        if (isset(Agent::TIER_HIERARCHY['ASSOCIATE']) && Agent::TIER_HIERARCHY['ASSOCIATE'] !== 0) {
            $issues[] = 'ASSOCIATE tier should have hierarchy position 0 (lowest tier)';
        }

        // Check if ASSOCIATE tier has reasonable commission rate
        if (isset(Agent::TIERS['ASSOCIATE'])) {
            $rate = Agent::TIERS['ASSOCIATE'];
            if ($rate < 10 || $rate > 30) {
                $issues[] = "ASSOCIATE tier commission rate ({$rate}%) seems unusual";
            }
        }

        return [
            'valid' => empty($issues),
            'issues' => $issues
        ];
    }
}
