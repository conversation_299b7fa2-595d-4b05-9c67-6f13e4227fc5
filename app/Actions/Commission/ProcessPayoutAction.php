<?php

namespace App\Actions\Commission;

use App\Actions\Action;
use App\Models\Agent;
use App\Models\AgentCommission;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Mail\CommissionPayoutNotification;

class ProcessPayoutAction extends Action
{
    /**
     * Process commission payouts for eligible agents.
     *
     * @param array $data
     * @return array
     */
    public function execute(array $data): array
    {
        try {
            DB::beginTransaction();

            $user = User::find($data['user_id']);
            
            // Check if user is eligible for commission payouts
            if (!$this->isEligibleForPayout($user)) {
                return [
                    'success' => false,
                    'message' => 'User is not eligible for commission payouts',
                    'reason' => $this->getIneligibilityReason($user)
                ];
            }

            // Get pending commissions for the user
            $pendingCommissions = $this->getPendingCommissions($user, $data);

            if ($pendingCommissions->isEmpty()) {
                return [
                    'success' => false,
                    'message' => 'No pending commissions found for payout'
                ];
            }

            // Calculate payout details
            $payoutDetails = $this->calculatePayoutDetails($pendingCommissions, $data);

            // Process the payout
            $payoutResult = $this->processPayout($user, $pendingCommissions, $payoutDetails, $data);

            if (!$payoutResult['success']) {
                DB::rollBack();
                return $payoutResult;
            }

            // Update commission statuses
            $this->updateCommissionStatuses($pendingCommissions, $payoutResult['payout_id']);

            // Send notifications
            $this->sendPayoutNotifications($user, $payoutDetails, $data);

            DB::commit();

            Log::info('Commission payout processed successfully', [
                'user_id' => $user->id,
                'payout_id' => $payoutResult['payout_id'],
                'total_amount' => $payoutDetails['total_amount'],
                'commission_count' => $pendingCommissions->count()
            ]);

            return [
                'success' => true,
                'message' => 'Commission payout processed successfully',
                'payout_id' => $payoutResult['payout_id'],
                'payout_details' => $payoutDetails,
                'commission_count' => $pendingCommissions->count()
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to process commission payout', [
                'user_id' => $data['user_id'] ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to process commission payout: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Check if user is eligible for commission payouts.
     *
     * @param User|null $user
     * @return bool
     */
    private function isEligibleForPayout(?User $user): bool
    {
        if (!$user) {
            return false;
        }

        // LOA users are not eligible for commission payouts
        if ($user->isLOA()) {
            return false;
        }

        // Must have eligible commission status
        if (!$user->isEligibleForCommission()) {
            return false;
        }

        // Agent must be approved and active
        if (!$user->agent || $user->agent->status !== 'approved') {
            return false;
        }

        return true;
    }

    /**
     * Get reason why user is not eligible for payout.
     *
     * @param User|null $user
     * @return string
     */
    private function getIneligibilityReason(?User $user): string
    {
        if (!$user) {
            return 'User not found';
        }

        if ($user->isLOA()) {
            return 'LOA users are not eligible for commission payouts';
        }

        if (!$user->agent) {
            return 'User does not have an agent profile';
        }

        if ($user->agent->status !== 'approved') {
            return 'Agent status is not approved';
        }

        if ($user->status !== 'active') {
            return 'User account is not active';
        }

        return 'Unknown eligibility issue';
    }

    /**
     * Get pending commissions for the user.
     *
     * @param User $user
     * @param array $data
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function getPendingCommissions(User $user, array $data)
    {
        $query = $user->agent->commissions()
            ->where('status', 'pending')
            ->with(['transaction', 'subscription']);

        // Apply date filters if provided
        if (isset($data['from_date'])) {
            $query->where('created_at', '>=', $data['from_date']);
        }

        if (isset($data['to_date'])) {
            $query->where('created_at', '<=', $data['to_date']);
        }

        // Apply minimum amount filter if provided
        if (isset($data['min_amount'])) {
            $query->where('commission_amount', '>=', $data['min_amount']);
        }

        return $query->get();
    }

    /**
     * Calculate payout details.
     *
     * @param \Illuminate\Database\Eloquent\Collection $commissions
     * @param array $data
     * @return array
     */
    private function calculatePayoutDetails($commissions, array $data): array
    {
        $totalAmount = $commissions->sum('commission_amount');
        $uplineAmount = $commissions->sum('upline_commission_amount');
        $netAmount = $totalAmount - $uplineAmount;

        // Apply any fees or deductions
        $processingFee = $data['processing_fee'] ?? 0;
        $finalAmount = $netAmount - $processingFee;

        return [
            'total_amount' => $totalAmount,
            'upline_amount' => $uplineAmount,
            'net_amount' => $netAmount,
            'processing_fee' => $processingFee,
            'final_amount' => $finalAmount,
            'commission_count' => $commissions->count(),
            'payout_method' => $data['payout_method'] ?? 'bank_transfer',
            'payout_date' => now()
        ];
    }

    /**
     * Process the actual payout.
     *
     * @param User $user
     * @param \Illuminate\Database\Eloquent\Collection $commissions
     * @param array $payoutDetails
     * @param array $data
     * @return array
     */
    private function processPayout(User $user, $commissions, array $payoutDetails, array $data): array
    {
        // Create payout record (you'd need to create a Payout model)
        $payoutData = [
            'agent_id' => $user->agent->id,
            'total_amount' => $payoutDetails['final_amount'],
            'commission_count' => $payoutDetails['commission_count'],
            'payout_method' => $payoutDetails['payout_method'],
            'status' => 'processed',
            'processed_at' => now(),
            'processed_by' => $data['processed_by'] ?? null,
            'reference_number' => $this->generatePayoutReference(),
            'notes' => $data['notes'] ?? null
        ];

        // For now, we'll just log the payout (in real implementation, create Payout model)
        $payoutId = DB::table('agent_payouts')->insertGetId($payoutData);

        // Here you would integrate with payment processor
        $paymentResult = $this->processPayment($user, $payoutDetails, $data);

        if (!$paymentResult['success']) {
            return [
                'success' => false,
                'message' => 'Payment processing failed: ' . $paymentResult['message']
            ];
        }

        return [
            'success' => true,
            'payout_id' => $payoutId,
            'payment_reference' => $paymentResult['reference']
        ];
    }

    /**
     * Update commission statuses to paid.
     *
     * @param \Illuminate\Database\Eloquent\Collection $commissions
     * @param int $payoutId
     * @return void
     */
    private function updateCommissionStatuses($commissions, int $payoutId): void
    {
        $commissionIds = $commissions->pluck('id')->toArray();
        
        AgentCommission::whereIn('id', $commissionIds)
            ->update([
                'status' => 'paid',
                'paid_at' => now(),
                'payout_id' => $payoutId
            ]);
    }

    /**
     * Send payout notifications.
     *
     * @param User $user
     * @param array $payoutDetails
     * @param array $data
     * @return void
     */
    private function sendPayoutNotifications(User $user, array $payoutDetails, array $data): void
    {
        try {
            if (class_exists(CommissionPayoutNotification::class)) {
                Mail::to($user->email)->send(new CommissionPayoutNotification($user, $payoutDetails));
            }

            // Send admin notification if requested
            if (isset($data['notify_admin']) && $data['notify_admin']) {
                // Send admin notification
            }

        } catch (\Exception $e) {
            Log::warning('Failed to send payout notifications', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Process payment through payment gateway.
     *
     * @param User $user
     * @param array $payoutDetails
     * @param array $data
     * @return array
     */
    private function processPayment(User $user, array $payoutDetails, array $data): array
    {
        // Mock payment processing - replace with actual payment gateway integration
        return [
            'success' => true,
            'reference' => 'PAY-' . strtoupper(substr(md5(uniqid(rand(), true)), 0, 10)),
            'message' => 'Payment processed successfully'
        ];
    }

    /**
     * Generate unique payout reference number.
     *
     * @return string
     */
    private function generatePayoutReference(): string
    {
        return 'PO-' . date('Ymd') . '-' . strtoupper(substr(md5(uniqid(rand(), true)), 0, 6));
    }
}
