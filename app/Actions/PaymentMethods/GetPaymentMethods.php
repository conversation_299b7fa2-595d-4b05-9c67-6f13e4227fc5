<?php

namespace App\Actions\PaymentMethods;

use App\Models\User;
use App\Models\PaymentMethod;
use Illuminate\Support\Facades\Log;

class GetPaymentMethods
{
    public function execute(array $data): array
    {
        $user = $data['user'];

        try {
            // Get credit cards from payment_methods table only
            $creditCards = $user->paymentMethods()
                ->where('type', 'credit_card')
                ->get()
                ->map(function ($method) {
                    return [
                        'id' => $method->id,
                        'brand' => $method->cc_brand,
                        'cc' => substr($method->cc_last_four, -4),
                        'expiration_month' => $method->cc_expiration_month,
                        'expiration_year' => $method->cc_expiration_year,
                        'is_default' => (bool) $method->is_default,
                        'source' => 'payment_methods'
                    ];
                });
info($creditCards);
            return [
                'success' => true,
                'credit_cards' => $creditCards
            ];
        } catch (\Exception $e) {
            Log::error('Error getting credit cards: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to get credit cards: ' . $e->getMessage(),
                'credit_cards' => []
            ];
        }
    }
}
