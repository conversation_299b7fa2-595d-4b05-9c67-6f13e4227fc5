<?php

namespace App\Actions\PaymentMethods;

use App\Models\PaymentMethod;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class RemovePaymentMethod
{
    public function __construct(
        private SetDefaultPaymentMethod $setDefaultPaymentMethod
    ) {}

    public function execute(array $data): array
    {
        $user = $data['user'];
        $paymentMethod = $data['payment_method'];

        try {
            // Handle PaymentMethod model only
            if ($paymentMethod instanceof PaymentMethod) {
                // Ensure the payment method belongs to the user
                if ($paymentMethod->user_id !== $user->id) {
                    return [
                        'success' => false,
                        'message' => 'This payment method does not belong to the user'
                    ];
                }

                // Check if this is the default payment method
                if ($paymentMethod->is_default) {
                    // Find another payment method to set as default
                    $anotherPaymentMethod = $user->paymentMethods()
                        ->where('id', '!=', $paymentMethod->id)
                        ->first();

                    if ($anotherPaymentMethod) {
                        $this->setDefaultPaymentMethod->execute([
                            'user' => $user,
                            'payment_method' => $anotherPaymentMethod
                        ]);
                    }
                }

                $paymentMethod->delete(); // This will soft delete the payment method
                $message = 'Payment method removed successfully';

            } else {
                return [
                    'success' => false,
                    'message' => 'Invalid payment method type - only PaymentMethod instances are supported'
                ];
            }

            return [
                'success' => true,
                'message' => $message
            ];
        } catch (\Exception $e) {
            Log::error('Error removing credit card: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to remove credit card: ' . $e->getMessage()
            ];
        }
    }
}
