<?php

namespace App\Concerns;

use App\Models\Agent;

trait HandlesCommissionTiers
{
    /**
     * Check if the user is eligible for commission calculations.
     * LOA role users are not eligible for commissions.
     *
     * @return bool
     */
    public function isEligibleForCommission(): bool
    {
        // LOA role users are not eligible for commissions
        if ($this->role === 'loa') {
            return false;
        }

        // Must have an agent profile to be eligible
        if (!$this->agent) {
            return false;
        }

        // Agent must be approved
        return $this->agent->status === 'approved';
    }

    /**
     * Check if the user is an LOA (referrer/encoder).
     *
     * @return bool
     */
    public function isLOA(): bool
    {
        return $this->role === 'loa';
    }

    /**
     * Get the agent's commission tier.
     *
     * @return string|null
     */
    public function getCommissionTier(): ?string
    {
        if (!$this->agent) {
            return null;
        }

        return $this->agent->tier;
    }

    /**
     * Get the commission rate for the agent's tier.
     *
     * @return float
     */
    public function getCommissionRate(): float
    {
        if (!$this->isEligibleForCommission()) {
            return 0.0;
        }

        $tier = $this->getCommissionTier();

        if (!$tier || !isset(Agent::TIERS[$tier])) {
            return 0.0;
        }

        return (float) Agent::TIERS[$tier];
    }

    /**
     * Check if the agent can be upgraded to a higher tier.
     *
     * @return bool
     */
    public function isEligibleForTierUpgrade(): bool
    {
        if (!$this->isEligibleForCommission()) {
            return false;
        }

        $currentTier = $this->getCommissionTier();

        if (!$currentTier) {
            return false;
        }

        // Can't upgrade if already at the highest tier
        $currentHierarchy = Agent::TIER_HIERARCHY[$currentTier] ?? -1;
        $maxHierarchy = max(Agent::TIER_HIERARCHY);

        return $currentHierarchy < $maxHierarchy;
    }

    /**
     * Get the next available tier for upgrade.
     *
     * @return string|null
     */
    public function getNextTier(): ?string
    {
        if (!$this->isEligibleForTierUpgrade()) {
            return null;
        }

        $currentTier = $this->getCommissionTier();
        $currentHierarchy = Agent::TIER_HIERARCHY[$currentTier] ?? -1;
        $nextHierarchy = $currentHierarchy + 1;

        foreach (Agent::TIER_HIERARCHY as $tier => $hierarchy) {
            if ($hierarchy === $nextHierarchy) {
                return $tier;
            }
        }

        return null;
    }

    /**
     * Check if the user can refer other agents.
     * LOA users can refer but don't earn commissions.
     *
     * @return bool
     */
    public function canReferAgents(): bool
    {
        return $this->role === 'agent' || $this->role === 'loa';
    }

    /**
     * Get tier-specific commission calculation rules.
     *
     * @return array
     */
    public function getTierCommissionRules(): array
    {
        $tier = $this->getCommissionTier();

        if (!$tier) {
            return [];
        }

        // Define tier-specific rules
        $rules = [
            'ASSOCIATE' => [
                'base_rate' => 20,
                'upline_eligible' => true,
                'can_have_downline' => false,
                'min_monthly_volume' => 0
            ],
            'AGENT' => [
                'base_rate' => 30,
                'upline_eligible' => true,
                'can_have_downline' => true,
                'min_monthly_volume' => 0
            ],
            'MGA' => [
                'base_rate' => 40,
                'upline_eligible' => true,
                'can_have_downline' => true,
                'min_monthly_volume' => 5000
            ],
            'SVG' => [
                'base_rate' => 45,
                'upline_eligible' => true,
                'can_have_downline' => true,
                'min_monthly_volume' => 10000
            ],
            'FMO' => [
                'base_rate' => 50,
                'upline_eligible' => true,
                'can_have_downline' => true,
                'min_monthly_volume' => 25000
            ],
            'SFMO' => [
                'base_rate' => 55,
                'upline_eligible' => false,
                'can_have_downline' => true,
                'min_monthly_volume' => 50000
            ]
        ];

        return $rules[$tier] ?? [];
    }
}
