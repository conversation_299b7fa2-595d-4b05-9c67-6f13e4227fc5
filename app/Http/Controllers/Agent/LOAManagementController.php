<?php

namespace App\Http\Controllers\Agent;

use App\Actions\Agent\CreateLOAUserAction;
use App\Http\Controllers\Controller;
use App\Http\Requests\CreateLOAUserRequest;
use App\Models\Agent;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use ProtoneMedia\Splade\Facades\Toast;
use ProtoneMedia\Splade\SpladeTable;

class LOAManagementController extends Controller
{
    protected $createLOAUserAction;

    public function __construct(CreateLOAUserAction $createLOAUserAction)
    {
        $this->createLOAUserAction = $createLOAUserAction;
    }

    /**
     * Display LOA management dashboard.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $agent = $user->agent;

        if (!$agent || !$agent->canManageLOAs()) {
            Toast::danger('You are not authorized to manage LOA users.');
            return redirect()->route('agent.dashboard');
        }

        // Get LOA statistics
        $statistics = $this->createLOAUserAction->getLOAStatistics($agent);

        // Get managed LOAs
        $managedLOAs = $agent->managedLOAs()->with(['managingAgent'])->get();

        // Create LOAs table
        $loasTable = SpladeTable::for($managedLOAs)
            ->column('fname', 'First Name')
            ->column('lname', 'Last Name')
            ->column('email', 'Email')
            ->column('phone', 'Phone')
            ->column('status', 'Status')
            ->column('created_at', 'Created')
            ->searchInput('email', 'Search by email')
            ->selectFilter('status', [
                'active' => 'Active',
                'inactive' => 'Inactive',
                'pending' => 'Pending'
            ])
            ->paginate(15);

        return view('agent.loa-management.index', [
            'agent' => $agent,
            'statistics' => $statistics,
            'loasTable' => $loasTable,
            'managedLOAs' => $managedLOAs
        ]);
    }

    /**
     * Show form to create new LOA user.
     */
    public function create(Request $request)
    {
        $user = Auth::user();
        $agent = $user->agent;

        if (!$agent || !$agent->canManageLOAs()) {
            Toast::danger('You are not authorized to create LOA users.');
            return redirect()->route('agent.loa-management.index');
        }

        $statistics = $this->createLOAUserAction->getLOAStatistics($agent);

        if ($statistics['remaining'] <= 0) {
            Toast::warning('You have reached the maximum number of LOA users for your tier.');
            return redirect()->route('agent.loa-management.index');
        }

        return view('agent.loa-management.create', [
            'agent' => $agent,
            'statistics' => $statistics
        ]);
    }

    /**
     * Store new LOA user.
     */
    public function store(CreateLOAUserRequest $request)
    {
        $user = Auth::user();
        $agent = $user->agent;

        $data = $request->validatedWithDefaults();
        $data['managing_agent_id'] = $agent->id;
        $data['created_by'] = $user->id;

        $result = $this->createLOAUserAction->execute($data);

        if ($result['success']) {
            Toast::success('LOA user created successfully!');
            return redirect()->route('agent.loa-management.index');
        }

        Toast::danger('Failed to create LOA user: ' . $result['message']);
        return back()->withInput();
    }

    /**
     * Show LOA user details.
     */
    public function show(Request $request, User $loaUser)
    {
        $user = Auth::user();
        $agent = $user->agent;

        // Verify this LOA belongs to the current agent
        if ($loaUser->managing_agent_id !== $agent->id) {
            Toast::danger('You can only view LOA users you manage.');
            return redirect()->route('agent.loa-management.index');
        }

        // Get LOA referral statistics
        $referralStats = $this->getLOAReferralStats($loaUser);

        return view('agent.loa-management.show', [
            'agent' => $agent,
            'loaUser' => $loaUser,
            'referralStats' => $referralStats
        ]);
    }

    /**
     * Show form to edit LOA user.
     */
    public function edit(Request $request, User $loaUser)
    {
        $user = Auth::user();
        $agent = $user->agent;

        // Verify this LOA belongs to the current agent
        if ($loaUser->managing_agent_id !== $agent->id) {
            Toast::danger('You can only edit LOA users you manage.');
            return redirect()->route('agent.loa-management.index');
        }

        return view('agent.loa-management.edit', [
            'agent' => $agent,
            'loaUser' => $loaUser
        ]);
    }

    /**
     * Update LOA user.
     */
    public function update(Request $request, User $loaUser)
    {
        $user = Auth::user();
        $agent = $user->agent;

        // Verify this LOA belongs to the current agent
        if ($loaUser->managing_agent_id !== $agent->id) {
            Toast::danger('You can only edit LOA users you manage.');
            return redirect()->route('agent.loa-management.index');
        }

        $validated = $request->validate([
            'fname' => 'required|string|max:255',
            'lname' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $loaUser->id,
            'phone' => 'nullable|string|max:20',
            'address1' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:255',
            'state' => 'nullable|string|max:2',
            'zip' => 'nullable|string|max:10',
            'status' => 'required|in:active,inactive'
        ]);

        $loaUser->update($validated);

        Toast::success('LOA user updated successfully!');
        return redirect()->route('agent.loa-management.show', $loaUser);
    }

    /**
     * Deactivate LOA user.
     */
    public function deactivate(Request $request, User $loaUser)
    {
        $user = Auth::user();
        $agent = $user->agent;

        // Verify this LOA belongs to the current agent
        if ($loaUser->managing_agent_id !== $agent->id) {
            Toast::danger('You can only manage LOA users you created.');
            return redirect()->route('agent.loa-management.index');
        }

        $loaUser->update(['status' => 'inactive']);

        Toast::success('LOA user deactivated successfully.');
        return redirect()->route('agent.loa-management.index');
    }

    /**
     * Get LOA referral statistics.
     */
    private function getLOAReferralStats(User $loaUser): array
    {
        // This would query the loa_referrals table
        return [
            'total_referrals' => 0, // DB query needed
            'pending_referrals' => 0,
            'converted_referrals' => 0,
            'this_month_referrals' => 0,
            'conversion_rate' => 0.0
        ];
    }
}
