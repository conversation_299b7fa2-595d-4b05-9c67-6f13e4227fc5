<?php

namespace App\Http\Controllers\LOA;

use App\Actions\Commission\ProcessLOAReferralAction;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreLOAReferralRequest;
use App\Models\Agent;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use ProtoneMedia\Splade\Facades\Toast;
use ProtoneMedia\Splade\SpladeTable;

class DashboardController extends Controller
{
    protected $processLOAReferralAction;

    public function __construct(ProcessLOAReferralAction $processLOAReferralAction)
    {
        $this->processLOAReferralAction = $processLOAReferralAction;
    }

    /**
     * Display the LOA dashboard.
     */
    public function index(Request $request)
    {
        $user = Auth::user();

        // Ensure user has LOA access
        if (!$user->canAccessLOADashboard()) {
            Toast::danger('Access denied. LOA dashboard is only available to LOA users.');
            return redirect()->route('home');
        }

        // Get LOA statistics
        $stats = $this->getLOAStatistics($user);

        // Get recent referrals
        $recentReferrals = $this->getRecentReferrals($user);

        // Get available agents for referrals
        $availableAgents = $this->getAvailableAgents();

        return view('loa.dashboard', [
            'user' => $user,
            'stats' => $stats,
            'recentReferrals' => $recentReferrals,
            'availableAgents' => $availableAgents,
            'navigation' => $user->getRoleNavigation()
        ]);
    }

    /**
     * Store a new LOA referral.
     */
    public function storeReferral(StoreLOAReferralRequest $request)
    {
        $result = $this->processLOAReferralAction->execute($request->validatedWithDefaults());

        if ($result['success']) {
            Toast::success('Referral created successfully! Tracking code: ' . $result['tracking_code']);
            
            return redirect()->route('loa.dashboard')->with([
                'referral_created' => true,
                'tracking_code' => $result['tracking_code']
            ]);
        }

        Toast::danger('Failed to create referral: ' . $result['message']);
        return back()->withInput();
    }

    /**
     * Display referrals analytics.
     */
    public function analytics(Request $request)
    {
        $user = Auth::user();

        if (!$user->canViewReferralAnalytics()) {
            Toast::danger('Access denied.');
            return redirect()->route('loa.dashboard');
        }

        $analytics = $this->getReferralAnalytics($user, $request);

        return view('loa.analytics', [
            'user' => $user,
            'analytics' => $analytics,
            'navigation' => $user->getRoleNavigation()
        ]);
    }

    /**
     * Display referrals list.
     */
    public function referrals(Request $request)
    {
        $user = Auth::user();

        // Get referrals table
        $referralsQuery = $this->getReferralsQuery($user);
        
        $referralsTable = SpladeTable::for($referralsQuery)
            ->column('tracking_code', 'Tracking Code')
            ->column('referral_type', 'Type')
            ->column('referral_name', 'Name')
            ->column('referral_email', 'Email')
            ->column('status', 'Status')
            ->column('created_at', 'Created')
            ->searchInput('referral_email', 'Search by email')
            ->selectFilter('referral_type', [
                'patient' => 'Patient',
                'business' => 'Business',
                'agent' => 'Agent'
            ])
            ->selectFilter('status', [
                'pending' => 'Pending',
                'contacted' => 'Contacted',
                'converted' => 'Converted',
                'declined' => 'Declined'
            ])
            ->paginate(15);

        return view('loa.referrals', [
            'user' => $user,
            'referralsTable' => $referralsTable,
            'navigation' => $user->getRoleNavigation()
        ]);
    }

    /**
     * Display LOA profile.
     */
    public function profile(Request $request)
    {
        $user = Auth::user();

        return view('loa.profile', [
            'user' => $user,
            'navigation' => $user->getRoleNavigation()
        ]);
    }

    /**
     * Update LOA profile.
     */
    public function updateProfile(Request $request)
    {
        $user = Auth::user();

        $validated = $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $user->id,
            'phone' => 'nullable|string|max:20',
            'address1' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:255',
            'state' => 'nullable|string|max:2',
            'zip' => 'nullable|string|max:10'
        ]);

        $user->update($validated);

        Toast::success('Profile updated successfully!');
        return redirect()->route('loa.profile');
    }

    /**
     * Get LOA statistics.
     */
    private function getLOAStatistics(User $user): array
    {
        // Mock data for now - replace with actual database queries
        return [
            'total_referrals' => $this->getTotalReferrals($user),
            'pending_referrals' => $this->getPendingReferrals($user),
            'converted_referrals' => $this->getConvertedReferrals($user),
            'this_month_referrals' => $this->getThisMonthReferrals($user),
            'referral_types' => $this->getReferralTypeBreakdown($user)
        ];
    }

    /**
     * Get recent referrals for dashboard.
     */
    private function getRecentReferrals(User $user)
    {
        // Mock implementation - replace with actual model query
        return collect([
            (object) [
                'id' => 1,
                'tracking_code' => 'LOA-ABC123',
                'referral_type' => 'patient',
                'referral_name' => 'John Doe',
                'referral_email' => '<EMAIL>',
                'status' => 'pending',
                'created_at' => now()->subDays(1)
            ],
            (object) [
                'id' => 2,
                'tracking_code' => 'LOA-DEF456',
                'referral_type' => 'business',
                'referral_name' => 'ABC Corp',
                'referral_email' => '<EMAIL>',
                'status' => 'contacted',
                'created_at' => now()->subDays(3)
            ]
        ]);
    }

    /**
     * Get available agents for referrals.
     */
    private function getAvailableAgents()
    {
        return Agent::with('user')
            ->where('status', 'approved')
            ->whereHas('user', function ($query) {
                $query->where('status', 'active');
            })
            ->get()
            ->mapWithKeys(function ($agent) {
                return [$agent->id => $agent->user->first_name . ' ' . $agent->user->last_name . ' (' . $agent->tier . ')'];
            });
    }

    /**
     * Get referral analytics data.
     */
    private function getReferralAnalytics(User $user, Request $request): array
    {
        $period = $request->get('period', '30'); // days

        return [
            'period' => $period,
            'total_referrals' => $this->getTotalReferrals($user, $period),
            'conversion_rate' => $this->getConversionRate($user, $period),
            'referrals_by_type' => $this->getReferralsByType($user, $period),
            'referrals_by_status' => $this->getReferralsByStatus($user, $period),
            'monthly_trend' => $this->getMonthlyTrend($user)
        ];
    }

    /**
     * Get referrals query for table.
     */
    private function getReferralsQuery(User $user)
    {
        // Mock implementation - replace with actual model query
        return collect([
            (object) [
                'tracking_code' => 'LOA-ABC123',
                'referral_type' => 'patient',
                'referral_name' => 'John Doe',
                'referral_email' => '<EMAIL>',
                'status' => 'pending',
                'created_at' => now()->subDays(1)
            ]
        ]);
    }

    // Helper methods for statistics
    private function getTotalReferrals(User $user, $period = null): int
    {
        // Mock implementation
        return 25;
    }

    private function getPendingReferrals(User $user): int
    {
        return 8;
    }

    private function getConvertedReferrals(User $user): int
    {
        return 12;
    }

    private function getThisMonthReferrals(User $user): int
    {
        return 15;
    }

    private function getReferralTypeBreakdown(User $user): array
    {
        return [
            'patient' => 15,
            'business' => 7,
            'agent' => 3
        ];
    }

    private function getConversionRate(User $user, $period): float
    {
        return 48.0; // 48%
    }

    private function getReferralsByType(User $user, $period): array
    {
        return [
            'patient' => 12,
            'business' => 5,
            'agent' => 2
        ];
    }

    private function getReferralsByStatus(User $user, $period): array
    {
        return [
            'pending' => 8,
            'contacted' => 6,
            'converted' => 4,
            'declined' => 1
        ];
    }

    private function getMonthlyTrend(User $user): array
    {
        return [
            'labels' => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            'data' => [5, 8, 12, 15, 18, 25]
        ];
    }
}
