<?php

namespace App\Http\Controllers\Patient;

use App\Actions\Subscriptions\CalculatePlanPrice;
use App\Actions\Subscriptions\ProcessSubscriptionPayment;
use App\Actions\Subscriptions\SubscribeToPlan;
use App\Http\Controllers\Controller;
use App\Models\CreditCard;
use App\Models\Discount;
use App\Models\OfflineConversion;
use App\Models\PaymentMethod;
use App\Models\SubscriptionPlan;
use App\Models\Transaction;
use App\Services\Api\AuthorizeNetApi;
use App\Services\Api\CustomerProfileService;
use App\Services\Api\PaymentProfileService;
use App\Services\TrialPlanService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use ProtoneMedia\Splade\Facades\Toast;

class SubscriptionController extends Controller
{
    /**
     * Constructor with action classes injected
     */
    public function __construct(
        private ProcessSubscriptionPayment $processSubscriptionPayment,
        private SubscribeToPlan $subscribeToPlan,
        private CalculatePlanPrice $calculatePlanPrice,
        private TrialPlanService $trialPlanService,
        private AuthorizeNetApi $authorizeNetApi
    ) {
        $this->customerProfileService = new CustomerProfileService($this->authorizeNetApi);
        $this->paymentProfileService = new PaymentProfileService($this->authorizeNetApi);
    }

    public function index()
    {
        $user = auth()->user();
        $activeSubscription = $user->activeSubscription();
        $currentPlan = $activeSubscription ? $activeSubscription->plan : null;
        $showAllPlans = !$currentPlan || !$currentPlan->isUnlimited();

        // Get the group_id from the current plan or default to group_id 3 (Featured Plans)
        $groupId = $currentPlan ? ($currentPlan->group_id ?? 3) : 3;

        // Query builder for subscription plans
        $plansQuery = SubscriptionPlan::active()->featured()->ordered();

        // If group_id is 2 (Free Trial), include both group 2 and group 3 plans
        // if ($groupId == 2) {
        //     $plansQuery->whereIn('group_id', [2, 3]);
        // } else {
            $plansQuery->where('group_id', $groupId);
        // }

        $role = $user->getRoleNames()->first();

        $plans = $plansQuery->get();

        return view('subscriptions.index', compact('plans', 'currentPlan', 'activeSubscription', 'showAllPlans', 'user', 'role'));
    }

    public function changePlan(Request $request, SubscriptionPlan $plan)
    {
        $result = $this->subscribeToPlan->execute([
            'user' => $request->user(),
            'plan' => $plan
        ]);

        if (!$result['success']) {
            Toast::danger($result['message']);
            return back();
        }

        // Calculate plan price
        $priceInfo = $this->calculatePlanPrice->execute([
            'user' => $request->user(),
            'plan' => $plan
        ]);

        // Redirect to payment page
        $request->session()->put('selected_plan_id', $plan->id);
        return redirect()->route('patient.payment');
    }

    public function showPaymentPage(Request $request)
    {
        $planId = $request->session()->get('selected_plan_id', 0);

        if ($planId == 0) {
            $planId = $request->user()->latestSubscription->plan_id ?? 1;
        }

        $plan = SubscriptionPlan::findOrFail($planId);

        // Calculate price information
        $priceInfo = $this->calculatePlanPrice->execute([
            'user' => $request->user(),
            'plan' => $plan,
            'discounted_price' => $request->session()->get('discounted_price')
        ]);

        return view('patient.payment', [
            'plan' => $plan,
            'originalPrice' => $priceInfo['original_price'],
            'discountedPrice' => $priceInfo['final_price'],
            'discountAmount' => $priceInfo['original_price'] - $priceInfo['final_price'],
            'discount' => $request->session()->has('discount_id') ? Discount::find($request->session()->get('discount_id')) : null
        ]);
    }

    public function processPayment(Request $request, SubscriptionPlan $plan)
    {
        $request->validate([
            'card_number' => 'required',
            'expiration_month' => 'required',
            'expiration_year' => 'required',
            'cvv' => 'required',
        ]);

        $user = $request->user();

        // Process the payment using the action
        $result = $this->processSubscriptionPayment->execute([
            'user' => $user,
            'plan' => $plan,
            'card_data' => [
                'card_number' => $request->card_number,
                'expiration_month' => $request->expiration_month,
                'expiration_year' => $request->expiration_year,
                'cvv' => $request->cvv,
            ],
            'session_data' => [
                'discounted_price' => $request->session()->get('discounted_price'),
                'source' => $request->session()->get('source'),
            ],
        ]);

        if ($result['success']) {
            // Clear the session data
            $request->session()->forget(['selected_plan_id', 'discounted_price']);

            Toast::info('Payment successful. Please record a short video about your condition.');

            // ShareASale tracking
            $shareASaleData = [
                'amount' => $result['amount'],
                'v' => 'gtm_1.0',
                'tracking' => $result['transaction_id'],
                'transtype' => 'sale',
                'merchantID' => env('SHAREASALE_MERCHANT_ID', ''),
            ];

            $baseUrl = 'https://www.shareasale.com/sale.cfm';
            $queryString = http_build_query($shareASaleData);
            $url = $baseUrl . '?' . $queryString;

            // Generate a unique identifier for this conversion
            $conversionId = uniqid('shareasale_');
            $request->session()->put('shareAsaleConversionId', $conversionId);

            // Store the URL and a flag indicating it hasn't been fired yet
            $request->session()->put($conversionId, [
                'url' => $url,
                'fired' => false
            ]);

            // Prepare conversion data
            $conversionData = [
                'gclid' => $request->cookie('gclid') ?? null,
                'conversion_name' => 'purchase',
                'conversion_value' => $result['amount'],
                'plan_id' => $plan->id,
                'transaction_id' => $result['transaction_id'],
                'user_id' => $user->id
            ];

            OfflineConversion::create($conversionData);

            return redirect()->route('patient.record-video.show')
                ->with('success', 'Payment successful. Please record a short video about your condition.');
        } else {
            Toast::danger('Payment failed: ' . $result['error'])->autoDismiss();
            return back()->with('error', 'Payment failed: ' . $result['error']);
        }
    }

    public function freePromo(Request $request)
    {
        $user = $request->user();
        $this->trialPlanService->startFree($user);

        // Clear the session data
        $request->session()->forget(['selected_plan_id', 'discounted_price']);

        Toast::info('Please record a short video about your condition.');
        return to_route('patient.record-video.show');
    }

    /**
     * Handle subscription request for a plan
     */
    public function subscribe(Request $request, SubscriptionPlan $plan)
    {
        $result = $this->subscribeToPlan->execute([
            'user' => $request->user(),
            'plan' => $plan
        ]);

        if (!$result['success']) {
            Toast::danger($result['message']);
            return back();
        }

        // Calculate plan price
        $priceInfo = $this->calculatePlanPrice->execute([
            'user' => $request->user(),
            'plan' => $plan,
            'discounted_price' => $request->session()->get('discounted_price')
        ]);

        // Store data in session
        $request->session()->put([
            'selected_plan_id' => $plan->id,
            'original_price' => $priceInfo['original_price'],
            'final_price' => $priceInfo['final_price']
        ]);

        // Redirect to the payment page
        Toast::info('Please complete your payment information to activate your subscription.');
        return redirect()->route('patient.payment');
    }

    public function cancel(Request $request)
    {
        $user = $request->user();
        $user->update([
            'subscription_plan_id' => null,
            'subscription_ends_at' => now(),
        ]);

        Toast::info('Subscription cancelled.')->autoDismiss(10);

        return redirect()->route('patient.subscriptions.index')->with('success', 'Subscription cancelled.');
    }

    public function renew(Request $request)
    {
        $user = $request->user();
        $plan = $user->subscriptionPlan;

        // Process the payment using the action
        $result = $this->processSubscriptionPayment->execute([
            'user' => $user,
            'plan' => $plan,
            'card_data' => [
                'card_number' => $request->card_number,
                'expiration_month' => $request->expiration_month,
                'expiration_year' => $request->expiration_year,
                'cvv' => $request->cvv,
            ],
        ]);

        if ($result['success']) {
            Toast::success('Subscription renewed!')->autoDismiss(10);
            return redirect()->route('patient.dashboard')->with('success', 'Subscription renewed!');
        } else {
            Toast::danger('Renewal failed: ' . $result['error']);
            return back()->with('error', 'Renewal failed: ' . $result['error']);
        }
    }

    /**
     * Show subscription history
     */
    public function history(Request $request)
    {
        $user = $request->user();
        $subscriptions = $user->subscriptions()->with('plan')->orderBy('created_at', 'desc')->get();
        $transactions = Transaction::where('user_id', $user->id)
            ->whereNotNull('subscription_id')
            ->orderBy('created_at', 'desc')
            ->get();

        return view('patient.subscription-history', compact('subscriptions', 'transactions'));
    }

    /**
     * Get credit cards for the authenticated user
     */
    public function getCreditCards(Request $request)
    {
        $user = Auth::user();
        $query = $user->creditCards();

        // Include trashed cards if requested
        if ($request->has('with_trashed') && $request->with_trashed) {
            $query = $user->allCreditCards();
        }

        $creditCards = $query->get()->map(function ($card) {
            return [
                'id' => $card->id,
                'brand' => $card->brand,
                'last_four' => substr($card->last_four, -4),
                'expiration_month' => $card->expiration_month,
                'expiration_year' => $card->expiration_year,
                'is_default' => (bool) $card->is_default,
                'deleted_at' => $card->deleted_at,
                'is_deleted' => $card->deleted_at !== null,
            ];
        });

        return response()->json($creditCards);
    }

    /**
     * Store a new credit card
     */
    public function storeCreditCard(Request $request)
    {
        $request->validate([
            'card_number' => 'required|string|min:13|max:19',
            'expiration_month' => 'required|string|size:2',
            'expiration_year' => 'required|string|size:4',
            'cvv' => 'required|string|min:3|max:4',
        ]);

        $user = Auth::user();

        try {
            // Get or create customer profile
            $customerProfileId = $user->authorize_net_customer_id;

            // Always create a new profile if there's an issue with the existing one
            try {
                if (!$customerProfileId) {
                    $customerProfileId = $this->customerProfileService->createCustomerProfile($user);
                    $user->authorize_net_customer_id = $customerProfileId;
                    $user->save();
                } else {
                    // Verify the customer profile exists in Authorize.net
                    if (!$this->customerProfileService->customerProfileExists($customerProfileId)) {
                        Log::warning("Customer profile {$customerProfileId} not found in Authorize.net. Creating new profile.");
                        $customerProfileId = $this->customerProfileService->createCustomerProfile($user);
                        $user->authorize_net_customer_id = $customerProfileId;
                        $user->save();
                    }
                }
            } catch (\Exception $e) {
                // If we get a duplicate profile error (E00039), try to extract the profile ID
                if (strpos($e->getMessage(), 'E00039') !== false && preg_match('/ID: (\d+)/', $e->getMessage(), $matches)) {
                    $customerProfileId = $matches[1];
                    $user->authorize_net_customer_id = $customerProfileId;
                    $user->save();
                    Log::info("Extracted existing profile ID {$customerProfileId} from duplicate error.");
                } else {
                    throw $e;
                }
            }

            // Create payment profile
            try {
                $paymentProfileId = $this->paymentProfileService->createPaymentProfile(
                    $customerProfileId,
                    $request->card_number,
                    $request->expiration_month,
                    $request->expiration_year,
                    $request->cvv,
                    $user
                );
            } catch (\Exception $e) {
                // If we get a duplicate payment profile error (E00039), try to extract the profile ID
                if (strpos($e->getMessage(), 'E00039') !== false && preg_match('/ID: (\d+)/', $e->getMessage(), $matches)) {
                    $paymentProfileId = $matches[1];
                    Log::info("Using existing payment profile ID {$paymentProfileId} from duplicate error.");
                } else {
                    throw $e;
                }
            }

            // Determine card brand
            $cardBrand = $this->getCardBrand($request->card_number);

            // Create or update credit card record
            $creditCard = $user->creditCards()->updateOrCreate(
                ['last_four' => substr($request->card_number, -4)],
                [
                    'user_id' => $user->id,
                    'token' => $paymentProfileId,
                    'brand' => $cardBrand,
                    'expiration_month' => $request->expiration_month,
                    'expiration_year' => $request->expiration_year,
                    'is_default' => $user->creditCards()->count() === 0 ? true : false
                ]
            );

            if ($creditCard->wasRecentlyCreated) {
                Log::info('New credit card added for user: ' . $user->id);
                Toast::success('Credit card added successfully');
            } else {
                Log::info('Existing credit card used for user: ' . $user->id);
                Toast::info('This credit card is already on file');
            }

            return back();
        } catch (\Exception $e) {
            Log::error('Error adding credit card: ' . $e->getMessage());
            Toast::danger('Failed to add credit card: ' . $e->getMessage());
            return back();
        }
    }

    /**
     * Set a credit card as default
     */
    public function setDefaultCreditCard(CreditCard $creditCard)
    {
        $user = Auth::user();

        // Ensure the credit card belongs to the authenticated user
        if ($creditCard->user_id !== $user->id) {
            Toast::danger('Unauthorized access');
            return back();
        }

        try {
            // Set all cards to non-default
            $user->creditCards()->update(['is_default' => false]);

            // Set the selected card as default
            $creditCard->is_default = true;
            $creditCard->save();
            Toast::success('Default payment method updated successfully');
            return back();
        } catch (\Exception $e) {
            Log::error('Error setting default credit card: ' . $e->getMessage());
            Toast::danger('Failed to update default payment method: ' . $e->getMessage());
            return back();
        }
    }

    /**
     * Soft delete a credit card
     */
    public function deleteCreditCard(CreditCard $creditCard)
    {
        $user = Auth::user();

        // Ensure the credit card belongs to the authenticated user
        if ($creditCard->user_id !== $user->id) {
            Toast::danger('Unauthorized access');
            return back();
        }

        try {
            // Check if this is the default card
            if ($creditCard->is_default) {
                // Find another card to set as default
                $anotherCard = $user->creditCards()->where('id', '!=', $creditCard->id)->first();
                if ($anotherCard) {
                    // Set all cards to non-default
                    $user->creditCards()->update(['is_default' => false]);

                    // Set the selected card as default
                    $anotherCard->is_default = true;
                    $anotherCard->save();
                }
            }

            $creditCard->delete(); // This will now soft delete the card
            Toast::success('Payment method removed successfully');
            return back();
        } catch (\Exception $e) {
            Log::error('Error removing credit card: ' . $e->getMessage());
            Toast::danger('Failed to remove payment method: ' . $e->getMessage());
            return back();
        }
    }

    /**
     * Permanently delete a credit card
     */
    public function forceDeleteCreditCard(Request $request, $id)
    {
        $user = Auth::user();
        $creditCard = CreditCard::withTrashed()->findOrFail($id);

        // Ensure the credit card belongs to the authenticated user
        if ($creditCard->user_id !== $user->id) {
            Toast::danger('Unauthorized access');
            return back();
        }

        try {
            $creditCard->forceDelete();
            Toast::success('Payment method permanently deleted');
            return back();
        } catch (\Exception $e) {
            Log::error('Error permanently deleting credit card: ' . $e->getMessage());
            Toast::danger('Failed to permanently delete payment method: ' . $e->getMessage());
            return back();
        }
    }

    /**
     * Restore a soft-deleted credit card
     */
    public function restoreCreditCard(Request $request, $id)
    {
        $user = Auth::user();
        $creditCard = CreditCard::withTrashed()->findOrFail($id);

        // Ensure the credit card belongs to the authenticated user
        if ($creditCard->user_id !== $user->id) {
            Toast::danger('Unauthorized access');
            return back();
        }

        try {
            $creditCard->restore();
            Toast::success('Payment method restored successfully');
            return back();
        } catch (\Exception $e) {
            Log::error('Error restoring credit card: ' . $e->getMessage());
            Toast::danger('Failed to restore payment method: ' . $e->getMessage());
            return back();
        }
    }

    /**
     * Determine the card brand based on the card number
     *
     * @param string $cardNumber
     * @return string
     */
    private function getCardBrand(string $cardNumber): string
    {
        $cardNumber = preg_replace('/\D/', '', $cardNumber);

        // Visa
        if (preg_match('/^4\d{12}(\d{3})?$/', $cardNumber)) {
            return 'Visa';
        }

        // MasterCard
        if (preg_match('/^(5[1-5]\d{4}|222[1-9]\d{2}|22[3-9]\d{3}|2[3-6]\d{4}|27[01]\d{3}|2720\d{2})\d{10}$/', $cardNumber)) {
            return 'MasterCard';
        }

        // American Express
        if (preg_match('/^3[47]\d{13}$/', $cardNumber)) {
            return 'American Express';
        }

        // Discover
        if (preg_match('/^(6011|65\d{2}|64[4-9]\d)\d{12}|(62\d{14})$/', $cardNumber)) {
            return 'Discover';
        }

        // JCB
        if (preg_match('/^35(28|29|[3-8]\d)\d{12}$/', $cardNumber)) {
            return 'JCB';
        }

        // Diners Club
        if (preg_match('/^3(0[0-5]|[68]\d)\d{11}$/', $cardNumber)) {
            return 'Diners Club';
        }

        return 'Unknown';
    }

    /**
     * Subscribe to a plan using a specific credit card
     */
    public function subscribeWithCard(Request $request, SubscriptionPlan $plan, PaymentMethod $creditCard)
    {
        $user = $request->user();

        // Ensure the credit card belongs to the authenticated user
        if ($creditCard->user_id !== $user->id) {
            Toast::danger('Unauthorized access');
            return back();
        }

        $result = $this->subscribeToPlan->execute([
            'user' => $user,
            'plan' => $plan
        ]);

        if (!$result['success']) {
            Toast::danger($result['message']);
            return back();
        }

        // Calculate plan price
        $priceInfo = $this->calculatePlanPrice->execute([
            'user' => $user,
            'plan' => $plan,
            'discounted_price' => $request->session()->get('discounted_price')
        ]);

        // Process the payment using the action and the selected credit card
        $result = $this->processSubscriptionPayment->execute([
            'user' => $user,
            'plan' => $plan,
            'use_existing_card' => true,
            'credit_card_id' => $creditCard->id,
            'session_data' => [
                'discounted_price' => $request->session()->get('discounted_price'),
                'source' => $request->session()->get('source'),
            ],
        ]);

        if ($result['success']) {
            // Clear the session data
            $request->session()->forget(['selected_plan_id', 'discounted_price']);

            Toast::success('Subscription activated successfully!');
            return redirect()->route('patient.dashboard');
        } else {
            Toast::danger('Payment failed: ' . $result['error'])->autoDismiss();
            return back();
        }
    }
}
