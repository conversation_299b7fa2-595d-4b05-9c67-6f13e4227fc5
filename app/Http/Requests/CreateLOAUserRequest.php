<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateLOAUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Only agents can create LOA users
        $user = $this->user();
        
        if (!$user || $user->role !== 'agent') {
            return false;
        }

        // Must have agent profile
        if (!$user->agent || $user->agent->status !== 'approved') {
            return false;
        }

        // Check if agent can create LOAs based on tier
        $allowedTiers = ['AGENT', 'MGA', 'SVG', 'FMO', 'SFMO'];
        return in_array($user->agent->tier, $allowedTiers);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'first_name' => [
                'required',
                'string',
                'max:255',
                'min:2'
            ],
            'last_name' => [
                'required',
                'string',
                'max:255',
                'min:2'
            ],
            'email' => [
                'required',
                'email',
                'max:255',
                'unique:users,email'
            ],
            'phone' => [
                'nullable',
                'string',
                'max:20',
                'regex:/^[\+]?[1-9][\d]{0,15}$/'
            ],
            'address1' => [
                'nullable',
                'string',
                'max:255'
            ],
            'city' => [
                'nullable',
                'string',
                'max:255'
            ],
            'state' => [
                'nullable',
                'string',
                'max:2',
                'regex:/^[A-Z]{2}$/'
            ],
            'zip' => [
                'nullable',
                'string',
                'max:10',
                'regex:/^\d{5}(-\d{4})?$/'
            ],
            'password' => [
                'nullable',
                'string',
                'min:8',
                'max:255'
            ],
            'send_welcome_email' => [
                'nullable',
                'boolean'
            ],
            'notify_agent' => [
                'nullable',
                'boolean'
            ]
        ];
    }

    /**
     * Get custom validation messages.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'first_name.required' => 'First name is required.',
            'first_name.min' => 'First name must be at least 2 characters.',
            'last_name.required' => 'Last name is required.',
            'last_name.min' => 'Last name must be at least 2 characters.',
            'email.required' => 'Email address is required.',
            'email.email' => 'Please enter a valid email address.',
            'email.unique' => 'This email address is already registered.',
            'phone.regex' => 'Please enter a valid phone number.',
            'state.regex' => 'State must be a 2-letter code (e.g., CA, NY).',
            'zip.regex' => 'Please enter a valid ZIP code (e.g., 12345 or 12345-6789).',
            'password.min' => 'Password must be at least 8 characters.'
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'first_name' => 'first name',
            'last_name' => 'last name',
            'email' => 'email address',
            'phone' => 'phone number',
            'address1' => 'address',
            'city' => 'city',
            'state' => 'state',
            'zip' => 'ZIP code',
            'password' => 'password',
            'send_welcome_email' => 'send welcome email',
            'notify_agent' => 'notify agent'
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Clean phone number
        if ($this->has('phone')) {
            $phone = preg_replace('/[^+\d]/', '', $this->phone);
            $this->merge(['phone' => $phone]);
        }

        // Normalize state to uppercase
        if ($this->has('state')) {
            $this->merge(['state' => strtoupper($this->state)]);
        }

        // Set default values
        $this->merge([
            'send_welcome_email' => $this->boolean('send_welcome_email', true),
            'notify_agent' => $this->boolean('notify_agent', true)
        ]);
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $this->validateAgentLimits($validator);
        });
    }

    /**
     * Validate agent LOA creation limits.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    protected function validateAgentLimits($validator): void
    {
        $agent = $this->user()->agent;
        
        if (!$agent) {
            return;
        }

        // Check current LOA count against tier limits
        $currentCount = $agent->managedLOAs()->count();
        $maxAllowed = $this->getMaxLOAsForTier($agent->tier);

        if ($currentCount >= $maxAllowed) {
            $validator->errors()->add(
                'general',
                "You have reached the maximum number of LOA users ({$maxAllowed}) for your {$agent->tier} tier."
            );
        }
    }

    /**
     * Get maximum LOAs allowed for each tier.
     *
     * @param string $tier
     * @return int
     */
    private function getMaxLOAsForTier(string $tier): int
    {
        $limits = [
            'AGENT' => 5,
            'MGA' => 15,
            'SVG' => 25,
            'FMO' => 50,
            'SFMO' => 100
        ];

        return $limits[$tier] ?? 0;
    }

    /**
     * Get the validated data with additional processing.
     *
     * @return array
     */
    public function validatedWithDefaults(): array
    {
        $validated = $this->validated();

        // Add managing agent ID
        $validated['managing_agent_id'] = $this->user()->agent->id;

        // Add created by
        $validated['created_by'] = $this->user()->id;

        return $validated;
    }
}
