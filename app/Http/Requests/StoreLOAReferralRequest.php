<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreLOAReferralRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Only LOA users can create LOA referrals
        return $this->user() && $this->user()->isLOA();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'referral_type' => [
                'required',
                'string',
                Rule::in(['patient', 'business', 'agent'])
            ],
            'referral_email' => [
                'required',
                'email',
                'max:255'
            ],
            'referral_name' => [
                'nullable',
                'string',
                'max:255'
            ],
            'referral_phone' => [
                'nullable',
                'string',
                'max:20',
                'regex:/^[\+]?[1-9][\d]{0,15}$/'
            ],
            'target_agent_id' => [
                'nullable',
                'integer',
                'exists:agents,id'
            ],
            'notes' => [
                'nullable',
                'string',
                'max:1000'
            ],
            'send_notification' => [
                'nullable',
                'boolean'
            ],
            'follow_up_date' => [
                'nullable',
                'date',
                'after:today'
            ]
        ];
    }

    /**
     * Get custom validation messages.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'referral_type.required' => 'Please select a referral type.',
            'referral_type.in' => 'Invalid referral type selected.',
            'referral_email.required' => 'Referral email is required.',
            'referral_email.email' => 'Please enter a valid email address.',
            'referral_phone.regex' => 'Please enter a valid phone number.',
            'target_agent_id.exists' => 'Selected agent does not exist.',
            'notes.max' => 'Notes cannot exceed 1000 characters.',
            'follow_up_date.after' => 'Follow-up date must be in the future.'
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'referral_type' => 'referral type',
            'referral_email' => 'referral email',
            'referral_name' => 'referral name',
            'referral_phone' => 'referral phone',
            'target_agent_id' => 'target agent',
            'notes' => 'notes',
            'send_notification' => 'send notification',
            'follow_up_date' => 'follow-up date'
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Clean phone number
        if ($this->has('referral_phone')) {
            $phone = preg_replace('/[^+\d]/', '', $this->referral_phone);
            $this->merge(['referral_phone' => $phone]);
        }

        // Ensure send_notification is boolean
        if ($this->has('send_notification')) {
            $this->merge(['send_notification' => (bool) $this->send_notification]);
        }
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Additional validation logic
            $this->validateTargetAgent($validator);
            $this->validateReferralType($validator);
        });
    }

    /**
     * Validate target agent selection.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    protected function validateTargetAgent($validator): void
    {
        if ($this->filled('target_agent_id')) {
            $agent = \App\Models\Agent::find($this->target_agent_id);
            
            if ($agent && $agent->status !== 'approved') {
                $validator->errors()->add(
                    'target_agent_id',
                    'Selected agent is not approved to receive referrals.'
                );
            }
        }
    }

    /**
     * Validate referral type specific requirements.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    protected function validateReferralType($validator): void
    {
        $referralType = $this->input('referral_type');

        // Business referrals should have company information
        if ($referralType === 'business' && !$this->filled('referral_name')) {
            $validator->errors()->add(
                'referral_name',
                'Company name is required for business referrals.'
            );
        }

        // Agent referrals should have target agent
        if ($referralType === 'agent' && !$this->filled('target_agent_id')) {
            $validator->errors()->add(
                'target_agent_id',
                'Target agent is required for agent referrals.'
            );
        }
    }

    /**
     * Get the validated data with additional processing.
     *
     * @return array
     */
    public function validatedWithDefaults(): array
    {
        $validated = $this->validated();

        // Add LOA user ID
        $validated['loa_user_id'] = $this->user()->id;

        // Set default values
        $validated['send_notification'] = $validated['send_notification'] ?? true;
        $validated['status'] = 'pending';

        return $validated;
    }
}
