<?php

namespace App\Http\Requests;

use App\Models\Agent;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateAgentTierRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Only admins and certain high-tier agents can update tiers
        $user = $this->user();
        
        if (!$user) {
            return false;
        }

        // Admins can always update tiers
        if ($user->role === 'admin') {
            return true;
        }

        // High-tier agents can update lower-tier agents
        if ($user->role === 'agent' && $user->agent) {
            $userTier = $user->agent->tier;
            $userHierarchy = Agent::TIER_HIERARCHY[$userTier] ?? -1;
            
            // Must be at least MGA level to update tiers
            return $userHierarchy >= Agent::TIER_HIERARCHY['MGA'];
        }

        return false;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $availableTiers = array_keys(Agent::TIERS);

        return [
            'tier' => [
                'required',
                'string',
                Rule::in($availableTiers)
            ],
            'reason' => [
                'required',
                'string',
                'max:500',
                'min:10'
            ],
            'effective_date' => [
                'nullable',
                'date',
                'after_or_equal:today'
            ],
            'notify_agent' => [
                'nullable',
                'boolean'
            ],
            'update_commission_rate' => [
                'nullable',
                'boolean'
            ]
        ];
    }

    /**
     * Get custom validation messages.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'tier.required' => 'Please select a tier.',
            'tier.in' => 'Invalid tier selected.',
            'reason.required' => 'Please provide a reason for the tier change.',
            'reason.min' => 'Reason must be at least 10 characters.',
            'reason.max' => 'Reason cannot exceed 500 characters.',
            'effective_date.after_or_equal' => 'Effective date cannot be in the past.'
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'tier' => 'tier',
            'reason' => 'reason',
            'effective_date' => 'effective date',
            'notify_agent' => 'notify agent',
            'update_commission_rate' => 'update commission rate'
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default values
        $this->merge([
            'notify_agent' => $this->boolean('notify_agent', true),
            'update_commission_rate' => $this->boolean('update_commission_rate', true),
            'effective_date' => $this->effective_date ?: now()->toDateString()
        ]);
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $this->validateTierChange($validator);
            $this->validateUserPermissions($validator);
        });
    }

    /**
     * Validate the tier change is appropriate.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    protected function validateTierChange($validator): void
    {
        $agent = $this->route('agent');
        $newTier = $this->input('tier');

        if (!$agent || !$newTier) {
            return;
        }

        $currentTier = $agent->tier;
        $currentHierarchy = Agent::TIER_HIERARCHY[$currentTier] ?? -1;
        $newHierarchy = Agent::TIER_HIERARCHY[$newTier] ?? -1;

        // Validate tier change logic
        if ($currentTier === $newTier) {
            $validator->errors()->add(
                'tier',
                'Agent is already at the selected tier.'
            );
        }

        // Check if downgrade requires special permission
        if ($newHierarchy < $currentHierarchy) {
            $this->validateTierDowngrade($validator, $agent, $currentTier, $newTier);
        }

        // Check if upgrade meets requirements
        if ($newHierarchy > $currentHierarchy) {
            $this->validateTierUpgrade($validator, $agent, $currentTier, $newTier);
        }
    }

    /**
     * Validate tier downgrade.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @param  \App\Models\Agent  $agent
     * @param  string  $currentTier
     * @param  string  $newTier
     * @return void
     */
    protected function validateTierDowngrade($validator, $agent, $currentTier, $newTier): void
    {
        // Only admins can downgrade tiers without special justification
        if ($this->user()->role !== 'admin') {
            $validator->errors()->add(
                'tier',
                'Only administrators can downgrade agent tiers.'
            );
        }

        // Require detailed reason for downgrades
        $reason = $this->input('reason', '');
        if (strlen($reason) < 50) {
            $validator->errors()->add(
                'reason',
                'Tier downgrades require a detailed reason (minimum 50 characters).'
            );
        }
    }

    /**
     * Validate tier upgrade.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @param  \App\Models\Agent  $agent
     * @param  string  $currentTier
     * @param  string  $newTier
     * @return void
     */
    protected function validateTierUpgrade($validator, $agent, $currentTier, $newTier): void
    {
        // Check if agent meets upgrade requirements
        $tierRules = $this->user()->getTierCommissionRules();
        
        if (isset($tierRules['min_monthly_volume'])) {
            $monthlyVolume = $this->getAgentMonthlyVolume($agent);
            $requiredVolume = $tierRules['min_monthly_volume'];
            
            if ($monthlyVolume < $requiredVolume) {
                $validator->errors()->add(
                    'tier',
                    "Agent does not meet minimum monthly volume requirement of ${requiredVolume} for {$newTier} tier."
                );
            }
        }
    }

    /**
     * Validate user permissions for the specific tier change.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    protected function validateUserPermissions($validator): void
    {
        $user = $this->user();
        $newTier = $this->input('tier');

        // Non-admin agents can only promote to tiers below their own
        if ($user->role === 'agent' && $user->agent) {
            $userHierarchy = Agent::TIER_HIERARCHY[$user->agent->tier] ?? -1;
            $newTierHierarchy = Agent::TIER_HIERARCHY[$newTier] ?? -1;

            if ($newTierHierarchy >= $userHierarchy) {
                $validator->errors()->add(
                    'tier',
                    'You can only promote agents to tiers below your own level.'
                );
            }
        }
    }

    /**
     * Get agent's monthly commission volume.
     *
     * @param  \App\Models\Agent  $agent
     * @return float
     */
    protected function getAgentMonthlyVolume($agent): float
    {
        return $agent->commissions()
            ->where('created_at', '>=', now()->subMonth())
            ->where('status', 'paid')
            ->sum('commission_amount');
    }

    /**
     * Get the validated data with additional processing.
     *
     * @return array
     */
    public function validatedWithDefaults(): array
    {
        $validated = $this->validated();

        // Add metadata
        $validated['updated_by'] = $this->user()->id;
        $validated['updated_at'] = now();

        // Calculate new commission rate if requested
        if ($validated['update_commission_rate']) {
            $validated['new_commission_rate'] = Agent::TIERS[$validated['tier']];
        }

        return $validated;
    }
}
