<template>
    <div class="referral-form">
        <form @submit.prevent="submitForm" class="space-y-4">
            <!-- Referral Type Selection -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">Referral Type <span class="text-error">*</span></span>
                </label>
                <select
                    v-model="form.referral_type"
                    @change="onReferralTypeChange"
                    class="select select-bordered w-full"
                    :class="{ 'select-error': validationErrors.referral_type?.length }"
                >
                    <option value="">Select referral type</option>
                    <option v-for="(label, value) in referralTypes" :key="value" :value="value">
                        {{ label }}
                    </option>
                </select>
                <div v-if="validationErrors.referral_type?.length" class="label">
                    <span class="label-text-alt text-error">{{ validationErrors.referral_type[0] }}</span>
                </div>
            </div>

            <!-- Referral Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="form-control">
                    <label class="label">
                        <span class="label-text font-medium">
                            {{ getReferralNameLabel() }} <span class="text-error">*</span>
                        </span>
                    </label>
                    <input
                        v-model="form.referral_name"
                        type="text"
                        :placeholder="getReferralNamePlaceholder()"
                        class="input input-bordered w-full"
                        :class="{ 'input-error': validationErrors.referral_name?.length }"
                    />
                    <div v-if="validationErrors.referral_name?.length" class="label">
                        <span class="label-text-alt text-error">{{ validationErrors.referral_name[0] }}</span>
                    </div>
                </div>

                <div class="form-control">
                    <label class="label">
                        <span class="label-text font-medium">Email Address <span class="text-error">*</span></span>
                    </label>
                    <input
                        v-model="form.referral_email"
                        type="email"
                        placeholder="Enter email address"
                        class="input input-bordered w-full"
                        :class="{ 'input-error': validationErrors.referral_email?.length }"
                    />
                    <div v-if="validationErrors.referral_email?.length" class="label">
                        <span class="label-text-alt text-error">{{ validationErrors.referral_email[0] }}</span>
                    </div>
                </div>
            </div>

            <!-- Phone Number -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">Phone Number</span>
                </label>
                <input
                    v-model="form.referral_phone"
                    type="tel"
                    placeholder="Enter phone number"
                    class="input input-bordered w-full"
                    :class="{ 'input-error': validationErrors.referral_phone?.length }"
                />
                <div v-if="validationErrors.referral_phone?.length" class="label">
                    <span class="label-text-alt text-error">{{ validationErrors.referral_phone[0] }}</span>
                </div>
            </div>

            <!-- Target Agent (for agent referrals) -->
            <div v-if="form.referral_type === 'agent'" class="form-control">
                <label class="label">
                    <span class="label-text font-medium">Target Agent <span class="text-error">*</span></span>
                </label>
                <select
                    v-model="form.target_agent_id"
                    class="select select-bordered w-full"
                    :class="{ 'select-error': validationErrors.target_agent_id?.length }"
                >
                    <option value="">Select target agent</option>
                    <option v-for="(label, value) in availableAgents" :key="value" :value="value">
                        {{ label }}
                    </option>
                </select>
                <div v-if="validationErrors.target_agent_id?.length" class="label">
                    <span class="label-text-alt text-error">{{ validationErrors.target_agent_id[0] }}</span>
                </div>
                <label class="label">
                    <span class="label-text-alt">Select the agent who will handle this referral</span>
                </label>
            </div>

            <!-- Notes -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">Notes</span>
                </label>
                <textarea
                    v-model="form.notes"
                    placeholder="Add any additional notes about this referral..."
                    class="textarea textarea-bordered w-full h-24"
                    :class="{ 'textarea-error': validationErrors.notes?.length }"
                ></textarea>
                <div v-if="validationErrors.notes?.length" class="label">
                    <span class="label-text-alt text-error">{{ validationErrors.notes[0] }}</span>
                </div>
                <label class="label">
                    <span class="label-text-alt">Maximum 1000 characters</span>
                </label>
            </div>

            <!-- Options -->
            <div class="form-control">
                <label class="label cursor-pointer justify-start gap-3">
                    <input
                        v-model="form.send_notification"
                        type="checkbox"
                        class="checkbox checkbox-primary"
                    />
                    <span class="label-text">Send email notification to referral</span>
                </label>
            </div>

            <!-- Follow-up Date -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">Follow-up Date</span>
                </label>
                <input
                    v-model="form.follow_up_date"
                    type="date"
                    class="input input-bordered w-full"
                    :class="{ 'input-error': validationErrors.follow_up_date?.length }"
                    :min="tomorrow"
                />
                <div v-if="validationErrors.follow_up_date?.length" class="label">
                    <span class="label-text-alt text-error">{{ validationErrors.follow_up_date[0] }}</span>
                </div>
                <label class="label">
                    <span class="label-text-alt">Optional: Set a reminder to follow up</span>
                </label>
            </div>

            <!-- Submit Button -->
            <div class="form-control mt-6">
                <button
                    type="submit"
                    class="btn btn-primary w-full"
                    :class="{ 'loading': isProcessing }"
                    :disabled="isProcessing || !isFormValid"
                >
                    <svg v-if="!isProcessing" class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    {{ isProcessing ? 'Creating Referral...' : 'Create Referral' }}
                </button>
            </div>
        </form>
    </div>
</template>

<script setup>
import { computed, reactive, ref, inject } from 'vue'

const Splade = inject('$splade')

// Props
const props = defineProps({
    availableAgents: {
        type: Object,
        default: () => ({})
    },
    submitUrl: {
        type: String,
        required: true
    }
})

// State
const isProcessing = ref(false)
const validationErrors = ref({
    referral_type: [],
    referral_name: [],
    referral_email: [],
    referral_phone: [],
    target_agent_id: [],
    notes: [],
    follow_up_date: []
})

// Form data
const form = reactive({
    referral_type: '',
    referral_name: '',
    referral_email: '',
    referral_phone: '',
    target_agent_id: '',
    notes: '',
    send_notification: true,
    follow_up_date: ''
})

// Referral types
const referralTypes = {
    'patient': 'Patient Referral',
    'business': 'Business Referral',
    'agent': 'Agent Referral'
}

// Computed properties
const tomorrow = computed(() => {
    const date = new Date()
    date.setDate(date.getDate() + 1)
    return date.toISOString().split('T')[0]
})

const isFormValid = computed(() => {
    const hasRequiredFields = form.referral_type && form.referral_name && form.referral_email

    if (form.referral_type === 'agent') {
        return hasRequiredFields && form.target_agent_id
    }

    return hasRequiredFields
})

// Methods
const getReferralNameLabel = () => {
    switch (form.referral_type) {
        case 'patient':
            return 'Patient Name'
        case 'business':
            return 'Company Name'
        case 'agent':
            return 'Agent Name'
        default:
            return 'Name'
    }
}

const getReferralNamePlaceholder = () => {
    switch (form.referral_type) {
        case 'patient':
            return 'Enter patient full name'
        case 'business':
            return 'Enter company name'
        case 'agent':
            return 'Enter agent full name'
        default:
            return 'Enter name'
    }
}

const onReferralTypeChange = () => {
    // Clear target agent when changing referral type
    if (form.referral_type !== 'agent') {
        form.target_agent_id = ''
    }
}

// Submit form using Splade
const submitForm = async () => {
    if (!isFormValid.value || isProcessing.value) {
        return
    }

    isProcessing.value = true
    validationErrors.value = {
        referral_type: [],
        referral_name: [],
        referral_email: [],
        referral_phone: [],
        target_agent_id: [],
        notes: [],
        follow_up_date: []
    }

    try {
        await Splade.request(props.submitUrl, 'post', form)

        // Reset form on success
        Object.assign(form, {
            referral_type: '',
            referral_name: '',
            referral_email: '',
            referral_phone: '',
            target_agent_id: '',
            notes: '',
            send_notification: true,
            follow_up_date: ''
        })

        console.log('Referral created successfully')
    } catch (error) {
        console.error('Error creating referral:', error)
        if (error.response && error.response.data && error.response.data.errors) {
            validationErrors.value = error.response.data.errors
        }
    } finally {
        isProcessing.value = false
    }
}
</script>
