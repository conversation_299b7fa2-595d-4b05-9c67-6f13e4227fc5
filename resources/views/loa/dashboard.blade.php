<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('LOA Dashboard') }}
        </h2>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Welcome Section -->
            <div class="bg-base-200 p-6 rounded-lg mb-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-base-content mb-2">
                            Welcome back, {{ $user->first_name }}!
                        </h1>
                        <p class="text-base-content/70">
                            Manage your referrals and track your performance as an LOA.
                        </p>
                    </div>
                    <div class="badge badge-info badge-lg">
                        LOA Dashboard
                    </div>
                </div>
            </div>

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <div class="card bg-base-100 shadow-md">
                    <div class="card-body">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-sm font-medium text-base-content/70">Total Referrals</h3>
                                <p class="text-2xl font-bold text-primary">{{ $stats['total_referrals'] }}</p>
                            </div>
                            <div class="badge badge-primary badge-lg">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM9 9a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card bg-base-100 shadow-md">
                    <div class="card-body">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-sm font-medium text-base-content/70">Pending</h3>
                                <p class="text-2xl font-bold text-warning">{{ $stats['pending_referrals'] }}</p>
                            </div>
                            <div class="badge badge-warning badge-lg">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card bg-base-100 shadow-md">
                    <div class="card-body">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-sm font-medium text-base-content/70">Converted</h3>
                                <p class="text-2xl font-bold text-success">{{ $stats['converted_referrals'] }}</p>
                            </div>
                            <div class="badge badge-success badge-lg">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card bg-base-100 shadow-md">
                    <div class="card-body">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-sm font-medium text-base-content/70">This Month</h3>
                                <p class="text-2xl font-bold text-info">{{ $stats['this_month_referrals'] }}</p>
                            </div>
                            <div class="badge badge-info badge-lg">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Referral Form -->
                <div class="lg:col-span-2">
                    <div class="card bg-base-100 shadow-md">
                        <div class="card-body">
                            <h2 class="card-title text-primary mb-4">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Create New Referral
                            </h2>
                            
                            <ReferralForm 
                                :available-agents="@js($availableAgents)"
                                :submit-url="@js(route('loa.referrals.store'))"
                            />
                        </div>
                    </div>
                </div>

                <!-- Quick Actions & Recent Referrals -->
                <div class="space-y-6">
                    <!-- Quick Actions -->
                    <div class="card bg-base-100 shadow-md">
                        <div class="card-body">
                            <h3 class="card-title text-sm mb-4">Quick Actions</h3>
                            <div class="space-y-2">
                                <x-splade-link 
                                    href="{{ route('loa.referrals') }}" 
                                    class="btn btn-outline btn-sm w-full justify-start"
                                >
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                    </svg>
                                    View All Referrals
                                </x-splade-link>
                                
                                <x-splade-link 
                                    href="{{ route('loa.analytics') }}" 
                                    class="btn btn-outline btn-sm w-full justify-start"
                                >
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                    View Analytics
                                </x-splade-link>
                                
                                <x-splade-link 
                                    href="{{ route('loa.profile') }}" 
                                    class="btn btn-outline btn-sm w-full justify-start"
                                >
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    Edit Profile
                                </x-splade-link>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Referrals -->
                    <div class="card bg-base-100 shadow-md">
                        <div class="card-body">
                            <h3 class="card-title text-sm mb-4">Recent Referrals</h3>
                            <div class="space-y-3">
                                @forelse($recentReferrals as $referral)
                                    <div class="flex items-center justify-between p-3 bg-base-200 rounded-lg">
                                        <div class="flex-1">
                                            <p class="font-medium text-sm">{{ $referral->referral_name }}</p>
                                            <p class="text-xs text-base-content/70">{{ $referral->referral_email }}</p>
                                            <div class="flex items-center gap-2 mt-1">
                                                <span class="badge badge-xs badge-outline">{{ $referral->referral_type }}</span>
                                                <span class="badge badge-xs 
                                                    @if($referral->status === 'pending') badge-warning
                                                    @elseif($referral->status === 'converted') badge-success
                                                    @elseif($referral->status === 'contacted') badge-info
                                                    @else badge-error
                                                    @endif
                                                ">{{ $referral->status }}</span>
                                            </div>
                                        </div>
                                        <div class="text-xs text-base-content/50">
                                            {{ $referral->created_at->diffForHumans() }}
                                        </div>
                                    </div>
                                @empty
                                    <div class="text-center py-4 text-base-content/50">
                                        <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                        </svg>
                                        <p class="text-sm">No recent referrals</p>
                                    </div>
                                @endforelse
                            </div>
                        </div>
                    </div>

                    <!-- Referral Type Breakdown -->
                    <div class="card bg-base-100 shadow-md">
                        <div class="card-body">
                            <h3 class="card-title text-sm mb-4">Referral Types</h3>
                            <div class="space-y-2">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm">Patients</span>
                                    <span class="badge badge-primary">{{ $stats['referral_types']['patient'] }}</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm">Businesses</span>
                                    <span class="badge badge-secondary">{{ $stats['referral_types']['business'] }}</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm">Agents</span>
                                    <span class="badge badge-accent">{{ $stats['referral_types']['agent'] }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
