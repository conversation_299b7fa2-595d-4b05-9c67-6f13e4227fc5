<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('My Referrals') }}
        </h2>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Page Header -->
            <div class="bg-base-200 p-6 rounded-lg mb-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-base-content mb-2">
                            My Referrals
                        </h1>
                        <p class="text-base-content/70">
                            Track and manage all your referrals in one place.
                        </p>
                    </div>
                    <x-splade-link 
                        href="{{ route('loa.dashboard') }}" 
                        class="btn btn-primary"
                    >
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        New Referral
                    </x-splade-link>
                </div>
            </div>

            <!-- Referrals Table -->
            <div class="card bg-base-100 shadow-md">
                <div class="card-body">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="card-title">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                            </svg>
                            All Referrals
                        </h2>
                        <div class="flex gap-2">
                            <x-splade-link 
                                href="{{ route('loa.analytics') }}" 
                                class="btn btn-outline btn-sm"
                            >
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                                Analytics
                            </x-splade-link>
                        </div>
                    </div>

                    <x-splade-table :for="$referralsTable" class="table table-zebra w-full">
                        <x-slot:cell(tracking_code)="{ item }">
                            <div class="font-mono text-sm">
                                <span class="badge badge-outline">{{ $item->tracking_code }}</span>
                            </div>
                        </x-slot:cell>

                        <x-slot:cell(referral_type)="{ item }">
                            <span class="badge 
                                @if($item->referral_type === 'patient') badge-primary
                                @elseif($item->referral_type === 'business') badge-secondary
                                @else badge-accent
                                @endif
                            ">
                                {{ ucfirst($item->referral_type) }}
                            </span>
                        </x-slot:cell>

                        <x-slot:cell(status)="{ item }">
                            <span class="badge 
                                @if($item->status === 'pending') badge-warning
                                @elseif($item->status === 'converted') badge-success
                                @elseif($item->status === 'contacted') badge-info
                                @else badge-error
                                @endif
                            ">
                                {{ ucfirst($item->status) }}
                            </span>
                        </x-slot:cell>

                        <x-slot:cell(created_at)="{ item }">
                            <div class="text-sm">
                                <div>{{ $item->created_at->format('M j, Y') }}</div>
                                <div class="text-base-content/50">{{ $item->created_at->format('g:i A') }}</div>
                            </div>
                        </x-slot:cell>

                        <x-slot:cell(actions)="{ item }">
                            <div class="flex gap-1">
                                <button class="btn btn-ghost btn-xs" onclick="viewReferral({{ $item->id }})">
                                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                </button>
                                <button class="btn btn-ghost btn-xs" onclick="copyTrackingCode('{{ $item->tracking_code }}')">
                                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                    </svg>
                                </button>
                            </div>
                        </x-slot:cell>
                    </x-splade-table>
                </div>
            </div>
        </div>
    </div>

    <script>
        function viewReferral(id) {
            // Implement view referral modal or page
            console.log('View referral:', id);
        }

        function copyTrackingCode(code) {
            navigator.clipboard.writeText(code).then(() => {
                // Show toast notification
                window.dispatchEvent(new CustomEvent('toast', {
                    detail: {
                        type: 'success',
                        message: 'Tracking code copied to clipboard!'
                    }
                }));
            });
        }
    </script>
</x-app-layout>
