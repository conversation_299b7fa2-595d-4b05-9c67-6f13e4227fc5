<?php

use App\Http\Controllers\LOA\DashboardController;
use Illuminate\Support\Facades\Route;

// LOA authenticated routes
Route::middleware(['auth', 'role:loa'])->prefix('loa')->name('loa.')->group(function () {
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    
    // Referrals
    Route::get('/referrals', [DashboardController::class, 'referrals'])->name('referrals');
    Route::post('/referrals', [DashboardController::class, 'storeReferral'])->name('referrals.store');
    
    // Analytics
    Route::get('/analytics', [DashboardController::class, 'analytics'])->name('analytics');
    
    // Profile
    Route::get('/profile', [DashboardController::class, 'profile'])->name('profile');
    Route::post('/profile', [DashboardController::class, 'updateProfile'])->name('profile.update');
});
