<?php

namespace Tests\Feature\Commission;

use App\Actions\Commission\AddAssociateTierAction;
use App\Models\Agent;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AssociateTierTest extends TestCase
{
    use RefreshDatabase;

    protected $addAssociateTierAction;

    protected function setUp(): void
    {
        parent::setUp();
        $this->addAssociateTierAction = app(AddAssociateTierAction::class);
    }

    /** @test */
    public function it_can_validate_associate_tier_configuration()
    {
        $result = $this->addAssociateTierAction->validateAssociateTierConfiguration();

        $this->assertTrue($result['valid']);
        $this->assertEmpty($result['issues']);
    }

    /** @test */
    public function associate_tier_exists_in_tiers_constant()
    {
        $this->assertArrayHasKey('ASSOCIATE', Agent::TIERS);
        $this->assertEquals(20, Agent::TIERS['ASSOCIATE']);
    }

    /** @test */
    public function associate_tier_exists_in_hierarchy_constant()
    {
        $this->assertArrayHasKey('ASSOCIATE', Agent::TIER_HIERARCHY);
        $this->assertEquals(0, Agent::TIER_HIERARCHY['ASSOCIATE']);
    }

    /** @test */
    public function associate_tier_has_lowest_hierarchy_position()
    {
        $associateHierarchy = Agent::TIER_HIERARCHY['ASSOCIATE'];
        $allHierarchyValues = array_values(Agent::TIER_HIERARCHY);
        
        $this->assertEquals(min($allHierarchyValues), $associateHierarchy);
    }

    /** @test */
    public function it_can_execute_add_associate_tier_action()
    {
        $result = $this->addAssociateTierAction->execute([]);

        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('tier_info', $result);
        $this->assertEquals('ASSOCIATE', $result['tier_info']['name']);
        $this->assertEquals(20, $result['tier_info']['rate']);
    }

    /** @test */
    public function associate_agent_can_calculate_commission()
    {
        $user = User::factory()->create(['role' => 'agent']);
        $agent = Agent::factory()->create([
            'user_id' => $user->id,
            'tier' => 'ASSOCIATE',
            'commission_rate' => 20,
            'status' => 'approved'
        ]);

        $transactionAmount = 1000.00;
        $expectedCommission = 200.00; // 20% of 1000

        $commission = $agent->calculateCommission($transactionAmount);

        $this->assertEquals($expectedCommission, $commission);
    }

    /** @test */
    public function associate_agent_commission_rate_is_correct()
    {
        $user = User::factory()->create(['role' => 'agent']);
        $agent = Agent::factory()->create([
            'user_id' => $user->id,
            'tier' => 'ASSOCIATE',
            'commission_rate' => 20,
            'status' => 'approved'
        ]);

        $this->assertEquals(20.0, $user->getCommissionRate());
        $this->assertEquals('ASSOCIATE', $user->getCommissionTier());
    }

    /** @test */
    public function associate_agent_is_eligible_for_commission()
    {
        $user = User::factory()->create(['role' => 'agent', 'status' => 'active']);
        $agent = Agent::factory()->create([
            'user_id' => $user->id,
            'tier' => 'ASSOCIATE',
            'status' => 'approved'
        ]);

        $this->assertTrue($user->isEligibleForCommission());
    }

    /** @test */
    public function associate_agent_can_be_upgraded_to_agent_tier()
    {
        $user = User::factory()->create(['role' => 'agent']);
        $agent = Agent::factory()->create([
            'user_id' => $user->id,
            'tier' => 'ASSOCIATE',
            'status' => 'approved'
        ]);

        $this->assertTrue($user->isEligibleForTierUpgrade());
        $this->assertEquals('AGENT', $user->getNextTier());
    }

    /** @test */
    public function associate_tier_commission_rules_are_correct()
    {
        $user = User::factory()->create(['role' => 'agent']);
        $agent = Agent::factory()->create([
            'user_id' => $user->id,
            'tier' => 'ASSOCIATE',
            'status' => 'approved'
        ]);

        $rules = $user->getTierCommissionRules();

        $this->assertEquals(20, $rules['base_rate']);
        $this->assertTrue($rules['upline_eligible']);
        $this->assertFalse($rules['can_have_downline']);
        $this->assertEquals(0, $rules['min_monthly_volume']);
    }

    /** @test */
    public function associate_agent_upline_commission_calculation()
    {
        // Create upline agent (AGENT tier)
        $uplineUser = User::factory()->create(['role' => 'agent']);
        $uplineAgent = Agent::factory()->create([
            'user_id' => $uplineUser->id,
            'tier' => 'AGENT',
            'commission_rate' => 30,
            'status' => 'approved'
        ]);

        // Create downline agent (ASSOCIATE tier)
        $downlineUser = User::factory()->create(['role' => 'agent']);
        $downlineAgent = Agent::factory()->create([
            'user_id' => $downlineUser->id,
            'tier' => 'ASSOCIATE',
            'commission_rate' => 20,
            'status' => 'approved',
            'referring_agent_id' => $uplineAgent->id
        ]);

        $transactionAmount = 1000.00;
        $expectedUplineCommission = 100.00; // (30% - 20%) * 1000

        $uplineCommission = $uplineAgent->calculateUplineCommission($transactionAmount, $downlineAgent);

        $this->assertEquals($expectedUplineCommission, $uplineCommission);
    }

    /** @test */
    public function associate_tier_appears_in_tier_hierarchy_correctly()
    {
        $expectedOrder = ['ASSOCIATE', 'AGENT', 'MGA', 'SVG', 'FMO', 'SFMO'];
        
        $actualOrder = collect(Agent::TIER_HIERARCHY)
            ->sortBy(function ($hierarchy, $tier) {
                return $hierarchy;
            })
            ->keys()
            ->toArray();

        $this->assertEquals($expectedOrder, $actualOrder);
    }
}
