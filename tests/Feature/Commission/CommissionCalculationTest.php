<?php

namespace Tests\Feature\Commission;

use App\Actions\Agents\CalculateCommissionAction;
use App\Models\Agent;
use App\Models\AgentCommission;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CommissionCalculationTest extends TestCase
{
    use RefreshDatabase;

    protected $calculateCommissionAction;

    protected function setUp(): void
    {
        parent::setUp();
        $this->calculateCommissionAction = app(CalculateCommissionAction::class);
    }

    /** @test */
    public function it_calculates_commission_for_associate_tier()
    {
        $user = User::factory()->create(['role' => 'agent', 'status' => 'active']);
        $agent = Agent::factory()->create([
            'user_id' => $user->id,
            'tier' => 'ASSOCIATE',
            'commission_rate' => 20,
            'status' => 'approved'
        ]);

        $transaction = Transaction::factory()->create(['amount' => 1000.00]);

        $result = $this->calculateCommissionAction->execute([
            'transaction_id' => $transaction->id,
            'agent_id' => $agent->id
        ]);

        $this->assertTrue($result['success']);
        $this->assertNotNull($result['commission']);
        $this->assertEquals(200.00, $result['commission']->commission_amount);
    }

    /** @test */
    public function it_does_not_calculate_commission_for_loa_user()
    {
        $loaUser = User::factory()->create(['role' => 'loa', 'status' => 'active']);
        $agent = Agent::factory()->create([
            'user_id' => $loaUser->id,
            'tier' => 'AGENT',
            'commission_rate' => 30,
            'status' => 'approved'
        ]);

        $transaction = Transaction::factory()->create(['amount' => 1000.00]);

        $result = $this->calculateCommissionAction->execute([
            'transaction_id' => $transaction->id,
            'agent_id' => $agent->id
        ]);

        $this->assertTrue($result['success']);
        $this->assertNull($result['commission']);
        $this->assertStringContainsString('not eligible for commission', $result['message']);
    }

    /** @test */
    public function it_calculates_upline_commission_correctly()
    {
        // Create upline agent (AGENT tier - 30%)
        $uplineUser = User::factory()->create(['role' => 'agent', 'status' => 'active']);
        $uplineAgent = Agent::factory()->create([
            'user_id' => $uplineUser->id,
            'tier' => 'AGENT',
            'commission_rate' => 30,
            'status' => 'approved'
        ]);

        // Create downline agent (ASSOCIATE tier - 20%)
        $downlineUser = User::factory()->create(['role' => 'agent', 'status' => 'active']);
        $downlineAgent = Agent::factory()->create([
            'user_id' => $downlineUser->id,
            'tier' => 'ASSOCIATE',
            'commission_rate' => 20,
            'status' => 'approved',
            'referring_agent_id' => $uplineAgent->id
        ]);

        $transaction = Transaction::factory()->create(['amount' => 1000.00]);

        $result = $this->calculateCommissionAction->execute([
            'transaction_id' => $transaction->id,
            'agent_id' => $downlineAgent->id
        ]);

        $this->assertTrue($result['success']);
        $this->assertNotNull($result['commission']);
        
        // Downline gets 20% = $200
        $this->assertEquals(200.00, $result['commission']->commission_amount);
        
        // Upline gets difference (30% - 20%) = 10% = $100
        $this->assertEquals(100.00, $result['commission']->upline_commission_amount);
        $this->assertEquals($uplineAgent->id, $result['commission']->upline_agent_id);
    }

    /** @test */
    public function commission_scope_excludes_loa_by_default()
    {
        // Create LOA user with agent profile
        $loaUser = User::factory()->create(['role' => 'loa']);
        $loaAgent = Agent::factory()->create([
            'user_id' => $loaUser->id,
            'tier' => 'AGENT',
            'status' => 'approved'
        ]);

        // Create regular agent
        $agentUser = User::factory()->create(['role' => 'agent']);
        $agent = Agent::factory()->create([
            'user_id' => $agentUser->id,
            'tier' => 'AGENT',
            'status' => 'approved'
        ]);

        // Create commissions for both
        $loaCommission = AgentCommission::factory()->create(['agent_id' => $loaAgent->id]);
        $agentCommission = AgentCommission::factory()->create(['agent_id' => $agent->id]);

        // Default scope should exclude LOA commissions
        $commissions = AgentCommission::all();
        
        $this->assertCount(1, $commissions);
        $this->assertEquals($agentCommission->id, $commissions->first()->id);
    }

    /** @test */
    public function commission_scope_can_include_loa_when_requested()
    {
        // Create LOA user with agent profile
        $loaUser = User::factory()->create(['role' => 'loa']);
        $loaAgent = Agent::factory()->create([
            'user_id' => $loaUser->id,
            'tier' => 'AGENT',
            'status' => 'approved'
        ]);

        // Create regular agent
        $agentUser = User::factory()->create(['role' => 'agent']);
        $agent = Agent::factory()->create([
            'user_id' => $agentUser->id,
            'tier' => 'AGENT',
            'status' => 'approved'
        ]);

        // Create commissions for both
        $loaCommission = AgentCommission::factory()->create(['agent_id' => $loaAgent->id]);
        $agentCommission = AgentCommission::factory()->create(['agent_id' => $agent->id]);

        // Include all agents should show both
        $commissions = AgentCommission::includeAllAgents()->get();
        
        $this->assertCount(2, $commissions);
    }

    /** @test */
    public function commission_calculation_respects_tier_hierarchy()
    {
        $tiers = [
            'ASSOCIATE' => 20,
            'AGENT' => 30,
            'MGA' => 40,
            'SVG' => 45,
            'FMO' => 50,
            'SFMO' => 55
        ];

        foreach ($tiers as $tier => $expectedRate) {
            $user = User::factory()->create(['role' => 'agent', 'status' => 'active']);
            $agent = Agent::factory()->create([
                'user_id' => $user->id,
                'tier' => $tier,
                'commission_rate' => $expectedRate,
                'status' => 'approved'
            ]);

            $transaction = Transaction::factory()->create(['amount' => 1000.00]);

            $result = $this->calculateCommissionAction->execute([
                'transaction_id' => $transaction->id,
                'agent_id' => $agent->id
            ]);

            $expectedCommission = 1000.00 * ($expectedRate / 100);

            $this->assertTrue($result['success'], "Failed for tier: {$tier}");
            $this->assertEquals($expectedCommission, $result['commission']->commission_amount, "Wrong commission for tier: {$tier}");
        }
    }

    /** @test */
    public function inactive_agent_does_not_get_commission()
    {
        $user = User::factory()->create(['role' => 'agent', 'status' => 'active']);
        $agent = Agent::factory()->create([
            'user_id' => $user->id,
            'tier' => 'AGENT',
            'commission_rate' => 30,
            'status' => 'pending' // Not approved
        ]);

        $transaction = Transaction::factory()->create(['amount' => 1000.00]);

        $result = $this->calculateCommissionAction->execute([
            'transaction_id' => $transaction->id,
            'agent_id' => $agent->id
        ]);

        $this->assertTrue($result['success']);
        $this->assertNull($result['commission']);
    }

    /** @test */
    public function commission_calculation_handles_zero_amount()
    {
        $user = User::factory()->create(['role' => 'agent', 'status' => 'active']);
        $agent = Agent::factory()->create([
            'user_id' => $user->id,
            'tier' => 'AGENT',
            'commission_rate' => 30,
            'status' => 'approved'
        ]);

        $transaction = Transaction::factory()->create(['amount' => 0.00]);

        $result = $this->calculateCommissionAction->execute([
            'transaction_id' => $transaction->id,
            'agent_id' => $agent->id
        ]);

        $this->assertTrue($result['success']);
        $this->assertNotNull($result['commission']);
        $this->assertEquals(0.00, $result['commission']->commission_amount);
    }

    /** @test */
    public function commission_calculation_rounds_correctly()
    {
        $user = User::factory()->create(['role' => 'agent', 'status' => 'active']);
        $agent = Agent::factory()->create([
            'user_id' => $user->id,
            'tier' => 'AGENT',
            'commission_rate' => 30,
            'status' => 'approved'
        ]);

        $transaction = Transaction::factory()->create(['amount' => 33.33]);

        $result = $this->calculateCommissionAction->execute([
            'transaction_id' => $transaction->id,
            'agent_id' => $agent->id
        ]);

        $this->assertTrue($result['success']);
        $this->assertNotNull($result['commission']);
        // 33.33 * 0.30 = 9.999, should round to 10.00
        $this->assertEquals(10.00, $result['commission']->commission_amount);
    }
}
