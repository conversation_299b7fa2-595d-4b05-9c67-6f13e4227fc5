<?php

namespace Tests\Feature\Commission;

use App\Actions\Agent\CreateLOAUserAction;
use App\Actions\Agents\CalculateCommissionAction;
use App\Actions\Commission\ProcessLOAReferralAction;
use App\Models\Agent;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class LOACommissionFlowTest extends TestCase
{
    use RefreshDatabase;

    protected $createLOAUserAction;
    protected $processLOAReferralAction;
    protected $calculateCommissionAction;

    protected function setUp(): void
    {
        parent::setUp();
        $this->createLOAUserAction = app(CreateLOAUserAction::class);
        $this->processLOAReferralAction = app(ProcessLOAReferralAction::class);
        $this->calculateCommissionAction = app(CalculateCommissionAction::class);
    }

    /** @test */
    public function agent_can_create_loa_user()
    {
        $agentUser = User::factory()->create(['role' => 'agent', 'status' => 'active']);
        $agent = Agent::factory()->create([
            'user_id' => $agentUser->id,
            'tier' => 'AGENT',
            'status' => 'approved'
        ]);

        $result = $this->createLOAUserAction->execute([
            'managing_agent_id' => $agent->id,
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'phone' => '1234567890'
        ]);

        $this->assertTrue($result['success']);
        $this->assertInstanceOf(User::class, $result['loa_user']);
        $this->assertEquals('loa', $result['loa_user']->role);
        $this->assertEquals($agent->id, $result['loa_user']->managing_agent_id);
    }

    /** @test */
    public function loa_user_belongs_to_managing_agent()
    {
        $agentUser = User::factory()->create(['role' => 'agent']);
        $agent = Agent::factory()->create([
            'user_id' => $agentUser->id,
            'tier' => 'AGENT',
            'status' => 'approved'
        ]);

        $loaUser = User::factory()->create([
            'role' => 'loa',
            'managing_agent_id' => $agent->id
        ]);

        $this->assertTrue($loaUser->hasManagingAgent());
        $this->assertEquals($agent->id, $loaUser->getManagingAgent()->id);
        $this->assertTrue($agent->managedLOAs->contains($loaUser));
    }

    /** @test */
    public function loa_referral_includes_managing_agent_tracking()
    {
        // Create managing agent
        $agentUser = User::factory()->create(['role' => 'agent']);
        $agent = Agent::factory()->create([
            'user_id' => $agentUser->id,
            'tier' => 'AGENT',
            'status' => 'approved'
        ]);
        $agent->generateReferralCode();

        // Create LOA user
        $loaUser = User::factory()->create([
            'role' => 'loa',
            'managing_agent_id' => $agent->id
        ]);

        $result = $this->processLOAReferralAction->execute([
            'loa_user_id' => $loaUser->id,
            'referral_type' => 'patient',
            'referral_email' => '<EMAIL>',
            'referral_name' => 'Jane Patient'
        ]);

        $this->assertTrue($result['success']);
        $this->assertStringContains('agent_ref=' . $agent->referral_code, $result['referral']->referral_url);
        $this->assertEquals($agent->id, $result['referral']->managing_agent_id ?? null);
    }

    /** @test */
    public function patient_referred_by_loa_generates_commission_for_managing_agent()
    {
        // Create managing agent
        $agentUser = User::factory()->create(['role' => 'agent', 'status' => 'active']);
        $agent = Agent::factory()->create([
            'user_id' => $agentUser->id,
            'tier' => 'AGENT',
            'commission_rate' => 30,
            'status' => 'approved'
        ]);

        // Create LOA user
        $loaUser = User::factory()->create([
            'role' => 'loa',
            'managing_agent_id' => $agent->id
        ]);

        // Create patient referred by LOA
        $patient = User::factory()->create([
            'role' => 'patient',
            'referring_agent_id' => $loaUser->id // Patient was referred by LOA
        ]);

        // Create transaction for the patient
        $transaction = Transaction::factory()->create([
            'user_id' => $patient->id,
            'amount' => 1000.00
        ]);

        // Calculate commission
        $result = $this->calculateCommissionAction->execute([
            'transaction_id' => $transaction->id
        ]);

        $this->assertTrue($result['success']);
        $this->assertNotNull($result['commission']);
        $this->assertEquals($agent->id, $result['commission']->agent_id);
        $this->assertEquals(300.00, $result['commission']->commission_amount); // 30% of 1000
    }

    /** @test */
    public function loa_user_cannot_earn_commission_directly()
    {
        // Create LOA user with agent profile (shouldn't happen but test edge case)
        $loaUser = User::factory()->create(['role' => 'loa']);
        $loaAgent = Agent::factory()->create([
            'user_id' => $loaUser->id,
            'tier' => 'AGENT',
            'commission_rate' => 30,
            'status' => 'approved'
        ]);

        $transaction = Transaction::factory()->create(['amount' => 1000.00]);

        $result = $this->calculateCommissionAction->execute([
            'transaction_id' => $transaction->id,
            'agent_id' => $loaAgent->id
        ]);

        $this->assertTrue($result['success']);
        $this->assertNull($result['commission']);
        $this->assertStringContainsString('not eligible for commission', $result['message']);
    }

    /** @test */
    public function agent_tier_limits_loa_creation()
    {
        // ASSOCIATE tier cannot create LOAs
        $associateUser = User::factory()->create(['role' => 'agent']);
        $associateAgent = Agent::factory()->create([
            'user_id' => $associateUser->id,
            'tier' => 'ASSOCIATE',
            'status' => 'approved'
        ]);

        $result = $this->createLOAUserAction->execute([
            'managing_agent_id' => $associateAgent->id,
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>'
        ]);

        $this->assertFalse($result['success']);
        $this->assertStringContainsString('not authorized', $result['message']);
    }

    /** @test */
    public function agent_can_view_loa_statistics()
    {
        $agentUser = User::factory()->create(['role' => 'agent']);
        $agent = Agent::factory()->create([
            'user_id' => $agentUser->id,
            'tier' => 'AGENT',
            'status' => 'approved'
        ]);

        // Create some LOA users
        User::factory()->count(3)->create([
            'role' => 'loa',
            'managing_agent_id' => $agent->id
        ]);

        $statistics = $this->createLOAUserAction->getLOAStatistics($agent);

        $this->assertEquals(3, $statistics['current_count']);
        $this->assertEquals(5, $statistics['max_allowed']); // AGENT tier limit
        $this->assertEquals(2, $statistics['remaining']);
    }

    /** @test */
    public function loa_referral_tracking_preserves_commission_chain()
    {
        // Create upline agent (MGA)
        $uplineUser = User::factory()->create(['role' => 'agent', 'status' => 'active']);
        $uplineAgent = Agent::factory()->create([
            'user_id' => $uplineUser->id,
            'tier' => 'MGA',
            'commission_rate' => 40,
            'status' => 'approved'
        ]);

        // Create downline agent (AGENT) under upline
        $agentUser = User::factory()->create(['role' => 'agent', 'status' => 'active']);
        $agent = Agent::factory()->create([
            'user_id' => $agentUser->id,
            'tier' => 'AGENT',
            'commission_rate' => 30,
            'status' => 'approved',
            'referring_agent_id' => $uplineAgent->id
        ]);

        // Create LOA under the downline agent
        $loaUser = User::factory()->create([
            'role' => 'loa',
            'managing_agent_id' => $agent->id
        ]);

        // Create patient referred by LOA
        $patient = User::factory()->create([
            'role' => 'patient',
            'referring_agent_id' => $loaUser->id
        ]);

        $transaction = Transaction::factory()->create([
            'user_id' => $patient->id,
            'amount' => 1000.00
        ]);

        $result = $this->calculateCommissionAction->execute([
            'transaction_id' => $transaction->id
        ]);

        $this->assertTrue($result['success']);
        $this->assertNotNull($result['commission']);
        
        // Downline agent gets 30% = $300
        $this->assertEquals($agent->id, $result['commission']->agent_id);
        $this->assertEquals(300.00, $result['commission']->commission_amount);
        
        // Upline agent gets difference (40% - 30%) = 10% = $100
        $this->assertEquals($uplineAgent->id, $result['commission']->upline_agent_id);
        $this->assertEquals(100.00, $result['commission']->upline_commission_amount);
    }

    /** @test */
    public function agent_can_manage_loas_based_on_tier()
    {
        $tiers = [
            'ASSOCIATE' => false,
            'AGENT' => true,
            'MGA' => true,
            'SVG' => true,
            'FMO' => true,
            'SFMO' => true
        ];

        foreach ($tiers as $tier => $canManage) {
            $user = User::factory()->create(['role' => 'agent', 'status' => 'active']);
            $agent = Agent::factory()->create([
                'user_id' => $user->id,
                'tier' => $tier,
                'status' => 'approved'
            ]);

            $this->assertEquals($canManage, $agent->canManageLOAs(), "Tier {$tier} should " . ($canManage ? 'be able to' : 'not be able to') . ' manage LOAs');
        }
    }
}
