<?php

namespace Tests\Feature\LOA;

use App\Models\User;
use App\Models\Agent;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class LOAAccessControlTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function loa_role_exists_in_user_roles()
    {
        $this->assertArrayHasKey('loa', User::ROLES);
        $this->assertEquals('LOA (Referrer/Encoder)', User::ROLES['loa']);
    }

    /** @test */
    public function loa_user_is_identified_correctly()
    {
        $loaUser = User::factory()->create(['role' => 'loa']);
        $agentUser = User::factory()->create(['role' => 'agent']);

        $this->assertTrue($loaUser->isLOA());
        $this->assertFalse($agentUser->isLOA());
    }

    /** @test */
    public function loa_user_is_not_eligible_for_commission()
    {
        $loaUser = User::factory()->create(['role' => 'loa', 'status' => 'active']);

        $this->assertFalse($loaUser->isEligibleForCommission());
    }

    /** @test */
    public function loa_user_cannot_access_commissions()
    {
        $loaUser = User::factory()->create(['role' => 'loa']);

        $this->assertFalse($loaUser->canAccessCommissions());
    }

    /** @test */
    public function loa_user_cannot_access_agent_dashboard()
    {
        $loaUser = User::factory()->create(['role' => 'loa']);

        $this->assertFalse($loaUser->canAccessAgentDashboard());
    }

    /** @test */
    public function loa_user_can_access_loa_dashboard()
    {
        $loaUser = User::factory()->create(['role' => 'loa']);

        $this->assertTrue($loaUser->canAccessLOADashboard());
    }

    /** @test */
    public function loa_user_can_create_referrals()
    {
        $loaUser = User::factory()->create(['role' => 'loa']);

        $this->assertTrue($loaUser->canCreateReferrals());
    }

    /** @test */
    public function loa_user_can_view_referral_analytics()
    {
        $loaUser = User::factory()->create(['role' => 'loa']);

        $this->assertTrue($loaUser->canViewReferralAnalytics());
    }

    /** @test */
    public function loa_user_cannot_manage_tiers()
    {
        $loaUser = User::factory()->create(['role' => 'loa']);

        $this->assertFalse($loaUser->canManageTiers());
    }

    /** @test */
    public function loa_user_gets_correct_dashboard_route()
    {
        $loaUser = User::factory()->create(['role' => 'loa']);

        $this->assertEquals('loa.dashboard', $loaUser->getDashboardRoute());
    }

    /** @test */
    public function agent_user_gets_correct_dashboard_route()
    {
        $agentUser = User::factory()->create(['role' => 'agent']);

        $this->assertEquals('agent.dashboard', $agentUser->getDashboardRoute());
    }

    /** @test */
    public function loa_user_gets_correct_navigation()
    {
        $loaUser = User::factory()->create(['role' => 'loa']);
        $navigation = $loaUser->getRoleNavigation();

        $this->assertArrayHasKey('dashboard', $navigation);
        $this->assertArrayHasKey('referrals', $navigation);
        $this->assertArrayHasKey('analytics', $navigation);
        $this->assertArrayHasKey('profile', $navigation);

        $this->assertEquals('loa.dashboard', $navigation['dashboard']['route']);
        $this->assertEquals('loa.referrals', $navigation['referrals']['route']);
        $this->assertEquals('loa.analytics', $navigation['analytics']['route']);
        $this->assertEquals('loa.profile', $navigation['profile']['route']);
    }

    /** @test */
    public function agent_user_gets_correct_navigation()
    {
        $agentUser = User::factory()->create(['role' => 'agent']);
        $navigation = $agentUser->getRoleNavigation();

        $this->assertArrayHasKey('dashboard', $navigation);
        $this->assertArrayHasKey('referrals', $navigation);
        $this->assertArrayHasKey('commissions', $navigation);
        $this->assertArrayHasKey('analytics', $navigation);
        $this->assertArrayHasKey('profile', $navigation);

        $this->assertEquals('agent.dashboard', $navigation['dashboard']['route']);
        $this->assertEquals('agent.commissions', $navigation['commissions']['route']);
    }

    /** @test */
    public function loa_user_accessible_features_are_correct()
    {
        $loaUser = User::factory()->create(['role' => 'loa']);
        $features = $loaUser->getAccessibleFeatures();

        $this->assertTrue($features['referrals']);
        $this->assertTrue($features['analytics']);
        $this->assertTrue($features['loa_dashboard']);
        $this->assertArrayNotHasKey('commissions', $features);
        $this->assertArrayNotHasKey('tier_management', $features);
        $this->assertArrayNotHasKey('agent_dashboard', $features);
    }

    /** @test */
    public function agent_user_accessible_features_are_correct()
    {
        $agentUser = User::factory()->create(['role' => 'agent', 'status' => 'active']);
        $agent = Agent::factory()->create([
            'user_id' => $agentUser->id,
            'status' => 'approved'
        ]);

        $features = $agentUser->getAccessibleFeatures();

        $this->assertTrue($features['referrals']);
        $this->assertTrue($features['analytics']);
        $this->assertTrue($features['commissions']);
        $this->assertTrue($features['tier_management']);
        $this->assertTrue($features['agent_dashboard']);
        $this->assertArrayNotHasKey('loa_dashboard', $features);
    }

    /** @test */
    public function loa_user_commission_rate_is_zero()
    {
        $loaUser = User::factory()->create(['role' => 'loa']);

        $this->assertEquals(0.0, $loaUser->getCommissionRate());
    }

    /** @test */
    public function loa_user_commission_tier_is_null()
    {
        $loaUser = User::factory()->create(['role' => 'loa']);

        $this->assertNull($loaUser->getCommissionTier());
    }

    /** @test */
    public function loa_user_is_not_eligible_for_tier_upgrade()
    {
        $loaUser = User::factory()->create(['role' => 'loa']);

        $this->assertFalse($loaUser->isEligibleForTierUpgrade());
    }

    /** @test */
    public function loa_user_next_tier_is_null()
    {
        $loaUser = User::factory()->create(['role' => 'loa']);

        $this->assertNull($loaUser->getNextTier());
    }

    /** @test */
    public function loa_user_can_refer_agents()
    {
        $loaUser = User::factory()->create(['role' => 'loa']);

        $this->assertTrue($loaUser->canReferAgents());
    }

    /** @test */
    public function loa_user_tier_commission_rules_are_empty()
    {
        $loaUser = User::factory()->create(['role' => 'loa']);

        $rules = $loaUser->getTierCommissionRules();

        $this->assertEmpty($rules);
    }
}
